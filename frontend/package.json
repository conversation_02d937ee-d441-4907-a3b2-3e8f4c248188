{"name": "holivia-frontend", "version": "1.0.0", "description": "Holivia - Local Public Holiday Reminder Mobile App", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.4.0", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.1", "axios": "^1.10.0", "date-fns": "^4.1.0", "expo": "~53.0.12", "expo-constants": "^17.1.6", "expo-dev-client": "~5.2.1", "expo-linear-gradient": "^14.1.5", "expo-notifications": "^0.31.3", "expo-status-bar": "~2.2.3", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-calendars": "^1.1312.1", "react-native-gesture-handler": "~2.24.0", "react-native-google-mobile-ads": "^15.4.0", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^14.0.0-rc.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@types/react-native-vector-icons": "^6.4.18", "typescript": "~5.8.3"}, "private": true}