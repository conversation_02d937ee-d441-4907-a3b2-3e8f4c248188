import { format, parseISO, isToday, isTomorrow, isYesterday } from 'date-fns';
import { Holiday, MarkedDates } from '../types';
import { COLORS, HOLIDAY_TYPES } from '../constants';

// Date formatting utilities
export const formatDate = (dateString: string, formatStr: string = 'PPP'): string => {
  try {
    const date = parseISO(dateString);
    return format(date, formatStr);
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
};

export const formatDateShort = (dateString: string): string => {
  return formatDate(dateString, 'MMM dd');
};

export const formatDateLong = (dateString: string): string => {
  return formatDate(dateString, 'EEEE, MMMM dd, yyyy');
};

export const formatDateWithDay = (dateString: string): string => {
  return formatDate(dateString, 'EEE, MMM dd');
};

// Calculate days until holiday
export const getDaysUntilHoliday = (dateString: string): number => {
  try {
    const holidayDate = parseISO(dateString);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to start of day
    holidayDate.setHours(0, 0, 0, 0); // Set to start of day

    const timeDiff = holidayDate.getTime() - today.getTime();
    const days = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

    return days;
  } catch (error) {
    console.error('Error calculating days until holiday:', error);
    return 0;
  }
};

// Get relative date description
export const getRelativeDateDescription = (dateString: string): string => {
  try {
    const date = parseISO(dateString);
    
    if (isToday(date)) {
      return 'Today';
    } else if (isTomorrow(date)) {
      return 'Tomorrow';
    } else if (isYesterday(date)) {
      return 'Yesterday';
    } else {
      const days = getDaysUntilHoliday(dateString);
      if (days > 0) {
        return `In ${days} day${days === 1 ? '' : 's'}`;
      } else if (days < 0) {
        return `${Math.abs(days)} day${Math.abs(days) === 1 ? '' : 's'} ago`;
      }
      return 'Today';
    }
  } catch (error) {
    console.error('Error getting relative date:', error);
    return '';
  }
};

// Holiday utilities
export const getHolidayTypeInfo = (type: string) => {
  return HOLIDAY_TYPES[type as keyof typeof HOLIDAY_TYPES] || {
    label: type,
    color: COLORS.gray[500],
    icon: 'calendar',
  };
};

export const getHolidayColor = (type: string): string => {
  return getHolidayTypeInfo(type).color;
};

// Convert holidays to marked dates for calendar
export const convertHolidaysToMarkedDates = (holidays: Holiday[]): MarkedDates => {
  const markedDates: MarkedDates = {};
  
  holidays.forEach((holiday) => {
    const dateKey = format(parseISO(holiday.date), 'yyyy-MM-dd');
    const typeInfo = getHolidayTypeInfo(holiday.type);
    
    markedDates[dateKey] = {
      marked: true,
      dotColor: typeInfo.color,
      activeOpacity: 0.7,
      customStyles: {
        container: {
          backgroundColor: typeInfo.color + '20', // 20% opacity
          borderRadius: 8,
        },
        text: {
          color: typeInfo.color,
          fontWeight: 'bold',
        },
      },
    };
  });
  
  return markedDates;
};

// Filter holidays by month
export const filterHolidaysByMonth = (holidays: Holiday[], year: number, month: number): Holiday[] => {
  return holidays.filter((holiday) => {
    const holidayDate = parseISO(holiday.date);
    return holidayDate.getFullYear() === year && holidayDate.getMonth() === month - 1;
  });
};

// Group holidays by month
export const groupHolidaysByMonth = (holidays: Holiday[]): { [key: string]: Holiday[] } => {
  const grouped: { [key: string]: Holiday[] } = {};
  
  holidays.forEach((holiday) => {
    const monthKey = format(parseISO(holiday.date), 'yyyy-MM');
    if (!grouped[monthKey]) {
      grouped[monthKey] = [];
    }
    grouped[monthKey].push(holiday);
  });
  
  return grouped;
};

// Sort holidays by date
export const sortHolidaysByDate = (holidays: Holiday[], ascending: boolean = true): Holiday[] => {
  return [...holidays].sort((a, b) => {
    const dateA = parseISO(a.date);
    const dateB = parseISO(b.date);
    return ascending ? dateA.getTime() - dateB.getTime() : dateB.getTime() - dateA.getTime();
  });
};

// Get upcoming holidays
export const getUpcomingHolidays = (holidays: Holiday[], limit: number = 5): Holiday[] => {
  const today = new Date();
  return holidays
    .filter((holiday) => parseISO(holiday.date) >= today)
    .sort((a, b) => parseISO(a.date).getTime() - parseISO(b.date).getTime())
    .slice(0, limit);
};

// Search holidays
export const searchHolidays = (holidays: Holiday[], query: string): Holiday[] => {
  const lowercaseQuery = query.toLowerCase();
  return holidays.filter((holiday) =>
    holiday.name.toLowerCase().includes(lowercaseQuery) ||
    holiday.description.toLowerCase().includes(lowercaseQuery) ||
    holiday.activities?.some(activity => activity.toLowerCase().includes(lowercaseQuery))
  );
};

// Validation utilities
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const isValidPhoneNumber = (phone: string): boolean => {
  const phoneRegex = /^\+?[\d\s-()]+$/;
  return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 10;
};

// String utilities
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
};

export const capitalizeFirstLetter = (text: string): string => {
  return text.charAt(0).toUpperCase() + text.slice(1);
};

export const capitalizeWords = (text: string): string => {
  return text.replace(/\w\S*/g, (txt) =>
    txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase()
  );
};

// Number utilities
export const formatNumber = (num: number): string => {
  return new Intl.NumberFormat().format(num);
};

// Debounce utility
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

// Month-end utilities
export interface MonthEndInfo {
  year: number;
  month: number;
  monthName: string;
  lastDay: Date;
  daysUntilEnd: number;
  isCurrentMonth: boolean;
}

export const getMonthEndDate = (year: number, month: number): Date => {
  // month is 1-based (1 = January, 12 = December)
  // Create date for last day of the month at end of day
  const lastDay = new Date(year, month, 0); // Day 0 of next month = last day of current month
  lastDay.setHours(23, 59, 59, 999); // Set to end of day
  return lastDay;
};

export const getMonthEndInfo = (year: number, month: number): MonthEndInfo => {
  const lastDay = getMonthEndDate(year, month);
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Set to start of day for accurate comparison

  // Calculate days more accurately
  const timeDiff = lastDay.getTime() - today.getTime();
  const daysUntilEnd = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
  const isCurrentMonth = today.getFullYear() === year && today.getMonth() === month - 1;

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  return {
    year,
    month,
    monthName: monthNames[month - 1],
    lastDay,
    daysUntilEnd,
    isCurrentMonth
  };
};

export const getCurrentMonthEndInfo = (): MonthEndInfo => {
  const today = new Date();
  return getMonthEndInfo(today.getFullYear(), today.getMonth() + 1);
};

export const getNextMonthEndInfo = (): MonthEndInfo => {
  const today = new Date();
  const nextMonth = today.getMonth() + 2; // +1 for next month, +1 because getMonth() is 0-based
  const year = nextMonth > 12 ? today.getFullYear() + 1 : today.getFullYear();
  const month = nextMonth > 12 ? 1 : nextMonth;

  return getMonthEndInfo(year, month);
};

export const shouldTriggerMonthEndReminder = (reminderDays: number[]): boolean => {
  const monthEndInfo = getCurrentMonthEndInfo();

  // Only trigger for current month
  if (!monthEndInfo.isCurrentMonth) {
    return false;
  }

  // Check if today matches any of the reminder days
  return reminderDays.includes(monthEndInfo.daysUntilEnd);
};

export const getMonthEndReminderMessage = (monthEndInfo: MonthEndInfo): string => {
  const { monthName, daysUntilEnd } = monthEndInfo;

  if (daysUntilEnd === 0) {
    return `Today is the last day of ${monthName}!`;
  } else if (daysUntilEnd === 1) {
    return `Tomorrow is the last day of ${monthName}!`;
  } else {
    return `${daysUntilEnd} days until the end of ${monthName}`;
  }
};

export const getUpcomingMonthEnds = (monthsAhead: number = 3): MonthEndInfo[] => {
  const today = new Date();
  const monthEnds: MonthEndInfo[] = [];

  for (let i = 0; i < monthsAhead; i++) {
    const targetDate = new Date(today.getFullYear(), today.getMonth() + i, 1);
    const year = targetDate.getFullYear();
    const month = targetDate.getMonth() + 1;

    monthEnds.push(getMonthEndInfo(year, month));
  }

  return monthEnds;
};
