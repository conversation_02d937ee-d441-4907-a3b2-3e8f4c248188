export interface Holiday {
  _id: string;
  name: string;
  date: string;
  year: number;
  country: string;
  type: 'national' | 'religious' | 'cultural' | 'regional';
  description: string;
  history?: string;
  activities?: string[];
  isRecurring: boolean;
  recurringPattern?: 'yearly' | 'lunar' | 'custom';
  imageUrl?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface HolidayResponse {
  success: boolean;
  data: Holiday[];
  pagination?: {
    current: number;
    pages: number;
    total: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  meta?: {
    query?: string;
    count?: number;
    year?: number;
    month?: number;
    country?: string;
  };
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface HolidayFilters {
  country?: string;
  year?: number;
  type?: string;
  month?: number;
  upcoming?: boolean;
  page?: number;
  limit?: number;
  search?: string;
}

export interface NavigationProps {
  navigation: any;
  route: any;
}

export interface CalendarDay {
  dateString: string;
  day: number;
  month: number;
  year: number;
  timestamp: number;
}

export interface MarkedDates {
  [date: string]: {
    marked?: boolean;
    dotColor?: string;
    activeOpacity?: number;
    selected?: boolean;
    selectedColor?: string;
    customStyles?: {
      container?: object;
      text?: object;
    };
  };
}
