import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert } from 'react-native';
import { Calendar } from 'react-native-calendars';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Holiday, MarkedDates } from '../types';
import { COLORS, SPACING, FONT_SIZES, STORAGE_KEYS } from '../constants';
import { useHolidays } from '../hooks/useHolidays';
import { convertHolidaysToMarkedDates, formatDateLong, getHolidayTypeInfo } from '../utils';
import LoadingSpinner from '../components/LoadingSpinner';

const CalendarScreen: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [selectedHolidays, setSelectedHolidays] = useState<Holiday[]>([]);
  const [markedDates, setMarkedDates] = useState<MarkedDates>({});
  const [selectedCountries, setSelectedCountries] = useState<string[]>(['Nigeria']);

  const {
    holidays,
    loading,
    error,
    fetchHolidays,
    fetchHolidaysForCountries,
    clearError,
  } = useHolidays();

  useEffect(() => {
    loadUserPreferences();
  }, []);

  useEffect(() => {
    if (selectedCountries.length > 0) {
      loadHolidays();
    }
  }, [selectedCountries]);

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error, [
        { text: 'OK', onPress: clearError },
      ]);
    }
  }, [error, clearError]);

  useEffect(() => {
    if (holidays.length > 0) {
      const marked = convertHolidaysToMarkedDates(holidays);
      setMarkedDates(marked);
    }
  }, [holidays]);

  const loadUserPreferences = async () => {
    try {
      const savedSettings = await AsyncStorage.getItem(STORAGE_KEYS.USER_PREFERENCES);
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings);
        if (parsed.selectedCountries && Array.isArray(parsed.selectedCountries)) {
          setSelectedCountries(parsed.selectedCountries);
        } else if (parsed.selectedCountry) {
          setSelectedCountries([parsed.selectedCountry]);
        }
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
    }
  };

  const loadHolidays = async () => {
    const currentYear = new Date().getFullYear();

    if (selectedCountries.length > 1) {
      await fetchHolidaysForCountries(selectedCountries, {
        year: currentYear,
        limit: 100
      });
    } else {
      await fetchHolidays({
        year: currentYear,
        limit: 100,
        country: selectedCountries[0] || 'Nigeria'
      });
    }
  };

  const onDayPress = (day: any) => {
    const dateString = day.dateString;
    setSelectedDate(dateString);

    // Find holidays for selected date
    const holidaysForDate = holidays.filter(holiday =>
      holiday.date.startsWith(dateString)
    );
    setSelectedHolidays(holidaysForDate);
  };

  const renderHolidayItem = (holiday: Holiday) => {
    const typeInfo = getHolidayTypeInfo(holiday.type);

    return (
      <View key={holiday._id} style={styles.holidayItem}>
        <LinearGradient
          colors={[typeInfo.color + '20', typeInfo.color + '10']}
          style={styles.holidayGradient}
        >
          <View style={styles.holidayContent}>
            <View style={styles.holidayHeader}>
              <Text style={styles.holidayName}>{holiday.name}</Text>
              <View style={[styles.typeBadge, { backgroundColor: typeInfo.color }]}>
                <Text style={styles.typeText}>{typeInfo.label}</Text>
              </View>
            </View>
            <Text style={styles.holidayDescription} numberOfLines={2}>
              {holiday.description}
            </Text>
          </View>
        </LinearGradient>
      </View>
    );
  };

  if (loading) {
    return <LoadingSpinner text="Loading calendar..." />;
  }

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}
    >
      {/* Header */}
      <LinearGradient
        colors={[COLORS.primary[600], COLORS.primary[700]]}
        style={styles.header}
      >
        <Text style={styles.headerTitle}>Holiday Calendar</Text>
        <Text style={styles.headerSubtitle}>
          Tap on a date to see holidays
        </Text>
      </LinearGradient>

      {/* Calendar */}
      <View style={styles.calendarContainer}>
        <Calendar
          onDayPress={onDayPress}
          markedDates={{
            ...markedDates,
            [selectedDate]: {
              ...markedDates[selectedDate],
              selected: true,
              selectedColor: COLORS.primary[600],
            },
          }}
          theme={{
            backgroundColor: COLORS.white,
            calendarBackground: COLORS.white,
            textSectionTitleColor: COLORS.gray[600],
            selectedDayBackgroundColor: COLORS.primary[600],
            selectedDayTextColor: COLORS.white,
            todayTextColor: COLORS.primary[600],
            dayTextColor: COLORS.gray[800],
            textDisabledColor: COLORS.gray[300],
            dotColor: COLORS.primary[600],
            selectedDotColor: COLORS.white,
            arrowColor: COLORS.primary[600],
            monthTextColor: COLORS.gray[800],
            indicatorColor: COLORS.primary[600],
            textDayFontWeight: '500',
            textMonthFontWeight: 'bold',
            textDayHeaderFontWeight: '600',
            textDayFontSize: 16,
            textMonthFontSize: 18,
            textDayHeaderFontSize: 14,
          }}
        />
      </View>

      {/* Selected Date Holidays */}
      {selectedDate && (
        <View style={styles.selectedDateContainer}>
          <Text style={styles.selectedDateTitle}>
            {formatDateLong(selectedDate)}
          </Text>

          {selectedHolidays.length > 0 ? (
            selectedHolidays.map(renderHolidayItem)
          ) : (
            <View style={styles.noHolidaysContainer}>
              <Ionicons
                name="calendar-outline"
                size={48}
                color={COLORS.gray[300]}
              />
              <Text style={styles.noHolidaysText}>
                No holidays on this date
              </Text>
            </View>
          )}
        </View>
      )}

      {/* Legend */}
      <View style={styles.legendContainer}>
        <Text style={styles.legendTitle}>Holiday Types</Text>
        <View style={styles.legendItems}>
          {Object.entries(COLORS.primary).slice(0, 4).map(([key, color], index) => {
            const types = ['National', 'Religious', 'Cultural', 'Regional'];
            return (
              <View key={key} style={styles.legendItem}>
                <View style={[styles.legendDot, { backgroundColor: color }]} />
                <Text style={styles.legendText}>{types[index]}</Text>
              </View>
            );
          })}
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.gray[50],
  },
  contentContainer: {
    paddingBottom: 100, // Extra padding for tab bar and phone navigation
  },
  header: {
    padding: SPACING.lg,
    paddingTop: SPACING.md,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: 'bold',
    color: COLORS.white,
    textAlign: 'center',
    marginBottom: SPACING.xs,
  },
  headerSubtitle: {
    fontSize: FONT_SIZES.md,
    color: COLORS.primary[100],
    textAlign: 'center',
  },
  calendarContainer: {
    margin: SPACING.md,
    borderRadius: 12,
    backgroundColor: COLORS.white,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedDateContainer: {
    margin: SPACING.md,
  },
  selectedDateTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: 'bold',
    color: COLORS.gray[800],
    marginBottom: SPACING.md,
    textAlign: 'center',
  },
  holidayItem: {
    marginBottom: SPACING.sm,
  },
  holidayGradient: {
    borderRadius: 8,
    padding: SPACING.md,
  },
  holidayContent: {
    flex: 1,
  },
  holidayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.xs,
  },
  holidayName: {
    fontSize: FONT_SIZES.md,
    fontWeight: 'bold',
    color: COLORS.gray[800],
    flex: 1,
    marginRight: SPACING.sm,
  },
  typeBadge: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: 12,
  },
  typeText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: '600',
    color: COLORS.white,
    textTransform: 'uppercase',
  },
  holidayDescription: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    lineHeight: 18,
  },
  noHolidaysContainer: {
    alignItems: 'center',
    padding: SPACING.xl,
  },
  noHolidaysText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[500],
    marginTop: SPACING.sm,
  },
  legendContainer: {
    margin: SPACING.md,
    padding: SPACING.md,
    backgroundColor: COLORS.white,
    borderRadius: 8,
  },
  legendTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: 'bold',
    color: COLORS.gray[800],
    marginBottom: SPACING.sm,
  },
  legendItems: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: SPACING.md,
    marginBottom: SPACING.xs,
  },
  legendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: SPACING.xs,
  },
  legendText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
  },
});

export default CalendarScreen;
