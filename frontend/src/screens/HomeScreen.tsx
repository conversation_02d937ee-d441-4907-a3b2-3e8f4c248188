import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Dimensions,
  StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Holiday } from '../types';
import { COLORS, SPACING, FONT_SIZES, APP_CONFIG, STORAGE_KEYS } from '../constants';
import { formatDateLong, getRelativeDateDescription, getCurrentMonthEndInfo, getMonthEndReminderMessage } from '../utils';
import LoadingSpinner from '../components/LoadingSpinner';
import BannerAdComponent from '../components/BannerAdComponent';
import apiService from '../services/api';
import adService from '../services/adService';

const { width } = Dimensions.get('window');

const HomeScreen: React.FC = () => {
  const navigation = useNavigation();
  const [nextHoliday, setNextHoliday] = useState<Holiday | null>(null);
  const [upcomingHolidays, setUpcomingHolidays] = useState<Holiday[]>([]);
  const [selectedCountry, setSelectedCountry] = useState<string>(APP_CONFIG.defaultCountry);
  const [selectedCountries, setSelectedCountries] = useState<string[]>([APP_CONFIG.defaultCountry]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [monthEndInfo, setMonthEndInfo] = useState(getCurrentMonthEndInfo());

  // We'll handle our own error clearing
  const clearError = () => setError(null);

  const loadUserPreferences = async () => {
    try {
      const savedSettings = await AsyncStorage.getItem(STORAGE_KEYS.USER_PREFERENCES);
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings);
        if (parsed.selectedCountry) {
          setSelectedCountry(parsed.selectedCountry);
        }
        if (parsed.selectedCountries && Array.isArray(parsed.selectedCountries)) {
          setSelectedCountries(parsed.selectedCountries);
        } else if (parsed.selectedCountry) {
          setSelectedCountries([parsed.selectedCountry]);
        }
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
    }
  };

  const loadUpcomingHolidays = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use multiple countries if available, otherwise fall back to single country
      const response = selectedCountries.length > 1
        ? await apiService.getUpcomingHolidaysForCountries(selectedCountries, 5)
        : await apiService.getUpcomingHolidays(selectedCountries[0] || selectedCountry, 5);

      if (response.success && response.data) {
        setUpcomingHolidays(response.data);
        if (response.data.length > 0) {
          setNextHoliday(response.data[0]);
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load holidays');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUserPreferences();
  }, []);

  useEffect(() => {
    if (selectedCountries.length > 0) {
      loadUpcomingHolidays();
    }
  }, [selectedCountries, selectedCountry]);

  // Refresh data when screen comes into focus (e.g., returning from settings)
  useFocusEffect(
    React.useCallback(() => {
      loadUserPreferences();
    }, [])
  );

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error, [
        { text: 'OK', onPress: clearError },
      ]);
    }
  }, [error, clearError]);

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      await loadUpcomingHolidays();
    } finally {
      setRefreshing(false);
    }
  };

  const navigateToHolidayDetail = (holiday: Holiday) => {
    (navigation as any).navigate('HolidayDetail', { holiday });
  };

  const navigateToCalendar = () => {
    (navigation as any).navigate('Calendar');
  };

  const navigateToAllHolidays = () => {
    (navigation as any).navigate('Holidays');
  };

  if (loading && !refreshing) {
    return <LoadingSpinner text="Loading holidays..." />;
  }

  const getCurrentGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const getDaysUntilHoliday = (date: string) => {
    const today = new Date();
    const holidayDate = new Date(date);
    const diffTime = holidayDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={COLORS.white} />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[COLORS.primary[600]]}
            tintColor={COLORS.primary[600]}
          />
        }
      >
        {/* Modern Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <View>
              <Text style={styles.greetingText}>{getCurrentGreeting()}</Text>
              <Text style={styles.appNameText}>Holivia</Text>
            </View>
            <TouchableOpacity style={styles.profileButton}>
              <View style={styles.profileIcon}>
                <Ionicons name="person" size={20} color={COLORS.primary[600]} />
              </View>
            </TouchableOpacity>
          </View>
        </View>

        {/* Next Holiday Hero Card */}
        {nextHoliday && (
          <View style={styles.heroCardContainer}>
            <TouchableOpacity
              style={styles.heroCard}
              onPress={() => navigateToHolidayDetail(nextHoliday)}
              activeOpacity={0.95}
            >
              <LinearGradient
                colors={[COLORS.primary[500], COLORS.primary[600], COLORS.primary[700]]}
                style={styles.heroCardGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <View style={styles.heroCardContent}>
                  <View style={styles.heroCardHeader}>
                    <Text style={styles.heroCardLabel}>Next Holiday</Text>
                    <View style={styles.heroCardBadge}>
                      <Text style={styles.heroCardBadgeText}>
                        {getDaysUntilHoliday(nextHoliday.date)} days
                      </Text>
                    </View>
                  </View>

                  <Text style={styles.heroCardTitle}>{nextHoliday.name}</Text>
                  <Text style={styles.heroCardDate}>
                    {formatDateLong(nextHoliday.date)}
                  </Text>

                  <View style={styles.heroCardFooter}>
                    <Text style={styles.heroCardDescription} numberOfLines={2}>
                      {nextHoliday.description}
                    </Text>
                    <Ionicons name="arrow-forward" size={20} color={COLORS.white} />
                  </View>
                </View>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        )}

        {/* Month-End Reminder Section */}
        {monthEndInfo.isCurrentMonth && monthEndInfo.daysUntilEnd <= 30 && (
          <View style={styles.monthEndSection}>
            <View style={styles.monthEndCard}>
              <View style={styles.monthEndHeader}>
                <View style={styles.monthEndIcon}>
                  <Ionicons name="calendar-outline" size={24} color={COLORS.accent[600]} />
                </View>
                <View style={styles.monthEndInfo}>
                  <Text style={styles.monthEndTitle}>Month End Reminder</Text>
                  <Text style={styles.monthEndMessage}>
                    {getMonthEndReminderMessage(monthEndInfo)}
                  </Text>
                </View>
                <View style={styles.monthEndBadge}>
                  <Text style={styles.monthEndBadgeText}>
                    {monthEndInfo.daysUntilEnd === 0 ? 'Today' : `${monthEndInfo.daysUntilEnd}d`}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        )}

        {/* Banner Ad */}
        <BannerAdComponent />

        {/* Modern Quick Actions */}
        <View style={styles.quickActionsSection}>
          <View style={styles.quickActionsGrid}>
            <TouchableOpacity
              style={styles.quickActionCard}
              onPress={navigateToCalendar}
              activeOpacity={0.8}
            >
              <View style={styles.quickActionIcon}>
                <Ionicons name="calendar-outline" size={24} color={COLORS.primary[600]} />
              </View>
              <Text style={styles.quickActionTitle}>Calendar</Text>
              <Text style={styles.quickActionSubtitle}>View all holidays</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.quickActionCard}
              onPress={navigateToAllHolidays}
              activeOpacity={0.8}
            >
              <View style={styles.quickActionIcon}>
                <Ionicons name="list-outline" size={24} color={COLORS.accent[600]} />
              </View>
              <Text style={styles.quickActionTitle}>Browse</Text>
              <Text style={styles.quickActionSubtitle}>All holidays</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.quickActionCard}
              onPress={() => (navigation as any).navigate('Search')}
              activeOpacity={0.8}
            >
              <View style={styles.quickActionIcon}>
                <Ionicons name="search-outline" size={24} color={COLORS.secondary[600]} />
              </View>
              <Text style={styles.quickActionTitle}>Search</Text>
              <Text style={styles.quickActionSubtitle}>Find holidays</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.quickActionCard}
              onPress={() => (navigation as any).navigate('Settings')}
              activeOpacity={0.8}
            >
              <View style={styles.quickActionIcon}>
                <Ionicons name="settings-outline" size={24} color={COLORS.gray[600]} />
              </View>
              <Text style={styles.quickActionTitle}>Settings</Text>
              <Text style={styles.quickActionSubtitle}>Preferences</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Upcoming Holidays Section */}
        <View style={styles.upcomingSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Upcoming</Text>
            <TouchableOpacity onPress={navigateToAllHolidays} style={styles.seeAllButton}>
              <Text style={styles.seeAllText}>See All</Text>
              <Ionicons name="chevron-forward" size={16} color={COLORS.primary[600]} />
            </TouchableOpacity>
          </View>

          {upcomingHolidays.length > 0 ? (
            <View style={styles.holidaysList}>
              {upcomingHolidays.slice(0, 4).map((holiday, index) => (
                <TouchableOpacity
                  key={holiday._id}
                  style={[
                    styles.holidayItem,
                    index === upcomingHolidays.slice(0, 4).length - 1 && styles.holidayItemLast
                  ]}
                  onPress={() => navigateToHolidayDetail(holiday)}
                  activeOpacity={0.7}
                >
                  <View style={styles.holidayItemLeft}>
                    <View style={styles.holidayDateContainer}>
                      <Text style={styles.holidayDay}>
                        {new Date(holiday.date).getDate()}
                      </Text>
                      <Text style={styles.holidayMonth}>
                        {new Date(holiday.date).toLocaleDateString('en', { month: 'short' })}
                      </Text>
                    </View>

                    <View style={styles.holidayInfo}>
                      <Text style={styles.holidayName} numberOfLines={1}>
                        {holiday.name}
                      </Text>
                      <Text style={styles.holidayType}>
                        {holiday.type} • {getRelativeDateDescription(holiday.date)}
                      </Text>
                    </View>
                  </View>

                  <Ionicons name="chevron-forward" size={20} color={COLORS.gray[400]} />
                </TouchableOpacity>
              ))}
            </View>
          ) : (
            <View style={styles.emptyState}>
              <View style={styles.emptyStateIcon}>
                <Ionicons name="calendar-outline" size={48} color={COLORS.gray[300]} />
              </View>
              <Text style={styles.emptyStateTitle}>No upcoming holidays</Text>
              <Text style={styles.emptyStateText}>
                Check back later for holiday updates
              </Text>
            </View>
          )}
        </View>

        {/* Bottom Banner Ad */}
        <BannerAdComponent />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 100, // Extra padding for tab bar
  },
  // Modern Header
  header: {
    paddingTop: 60,
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.lg,
    backgroundColor: COLORS.white,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  greetingText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[600],
    marginBottom: 4,
  },
  appNameText: {
    fontSize: FONT_SIZES.xxxl,
    fontWeight: '700',
    color: COLORS.gray[900],
  },
  profileButton: {
    padding: 8,
  },
  profileIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Hero Card
  heroCardContainer: {
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.xl,
  },
  heroCard: {
    borderRadius: 24,
    overflow: 'hidden',
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },
  heroCardGradient: {
    padding: SPACING.xl,
    minHeight: 180,
  },
  heroCardContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  heroCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  heroCardLabel: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.white,
    opacity: 0.9,
    fontWeight: '500',
  },
  heroCardBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  heroCardBadgeText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.white,
    fontWeight: '600',
  },
  heroCardTitle: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: '700',
    color: COLORS.white,
    marginBottom: SPACING.xs,
    lineHeight: 32,
  },
  heroCardDate: {
    fontSize: FONT_SIZES.md,
    color: COLORS.white,
    opacity: 0.9,
    marginBottom: SPACING.lg,
  },
  heroCardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  heroCardDescription: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.white,
    opacity: 0.9,
    flex: 1,
    marginRight: SPACING.md,
    lineHeight: 20,
  },
  // Quick Actions
  quickActionsSection: {
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.xl,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionCard: {
    width: (width - SPACING.lg * 2 - SPACING.md) / 2,
    backgroundColor: COLORS.white,
    borderRadius: 20,
    padding: SPACING.lg,
    marginBottom: SPACING.md,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
    borderWidth: 1,
    borderColor: COLORS.gray[100],
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: 12,
    backgroundColor: COLORS.gray[50],
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  quickActionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: 4,
  },
  quickActionSubtitle: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[500],
  },
  // Upcoming Section
  upcomingSection: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: '700',
    color: COLORS.gray[900],
  },
  seeAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  seeAllText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.primary[600],
    fontWeight: '600',
    marginRight: 4,
  },
  holidaysList: {
    backgroundColor: COLORS.white,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
    borderWidth: 1,
    borderColor: COLORS.gray[100],
  },
  holidayItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[50],
  },
  holidayItemLast: {
    borderBottomWidth: 0,
  },
  holidayItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  holidayDateContainer: {
    width: 60,
    alignItems: 'center',
    marginRight: SPACING.lg,
  },
  holidayDay: {
    fontSize: FONT_SIZES.xl,
    fontWeight: '700',
    color: COLORS.primary[600],
    lineHeight: 24,
  },
  holidayMonth: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[500],
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  holidayInfo: {
    flex: 1,
  },
  holidayName: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: 4,
  },
  holidayType: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[500],
    textTransform: 'capitalize',
  },
  // Month-End Section
  monthEndSection: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  monthEndCard: {
    backgroundColor: COLORS.white,
    borderRadius: 16,
    padding: SPACING.lg,
    borderWidth: 1,
    borderColor: COLORS.accent[100],
    shadowColor: COLORS.accent[600],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  monthEndHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  monthEndIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: COLORS.accent[50],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  monthEndInfo: {
    flex: 1,
  },
  monthEndTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: 4,
  },
  monthEndMessage: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    lineHeight: 18,
  },
  monthEndBadge: {
    backgroundColor: COLORS.accent[100],
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  monthEndBadgeText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: '600',
    color: COLORS.accent[700],
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  // Empty State
  emptyState: {
    alignItems: 'center',
    padding: SPACING.xxl,
    backgroundColor: COLORS.white,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: COLORS.gray[100],
  },
  emptyStateIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.gray[50],
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  emptyStateTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.gray[700],
    marginBottom: SPACING.sm,
  },
  emptyStateText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[500],
    textAlign: 'center',
    lineHeight: 22,
  },
});

export default HomeScreen;
