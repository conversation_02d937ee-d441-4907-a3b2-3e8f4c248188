import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Holiday } from '../types';
import { COLORS, SPACING, FONT_SIZES, HOLIDAY_TYPES, STORAGE_KEYS } from '../constants';
import { useHolidays } from '../hooks/useHolidays';
import { sortHolidaysByDate } from '../utils';
import LoadingSpinner from '../components/LoadingSpinner';
import HolidayCard from '../components/HolidayCard';
import BannerAdComponent from '../components/BannerAdComponent';

const HolidaysScreen: React.FC = () => {
  const navigation = useNavigation();
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedTimeFilter, setSelectedTimeFilter] = useState<string>('upcoming'); // 'upcoming' or 'all'
  const [filteredHolidays, setFilteredHolidays] = useState<Holiday[]>([]);
  const [selectedCountries, setSelectedCountries] = useState<string[]>(['Nigeria']);

  const {
    holidays,
    loading,
    error,
    refreshing,
    fetchHolidays,
    fetchHolidaysForCountries,
    refreshHolidays,
    loadMoreHolidays,
    clearError,
  } = useHolidays();

  useEffect(() => {
    loadUserPreferences();
  }, []);

  useEffect(() => {
    if (selectedCountries.length > 0) {
      loadAllHolidays();
    }
  }, [selectedCountries]);

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error, [
        { text: 'OK', onPress: clearError },
      ]);
    }
  }, [error, clearError]);

  useEffect(() => {
    filterHolidays();
  }, [holidays, selectedType, selectedTimeFilter]);

  const loadUserPreferences = async () => {
    try {
      const savedSettings = await AsyncStorage.getItem(STORAGE_KEYS.USER_PREFERENCES);
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings);
        if (parsed.selectedCountries && Array.isArray(parsed.selectedCountries)) {
          setSelectedCountries(parsed.selectedCountries);
        } else if (parsed.selectedCountry) {
          setSelectedCountries([parsed.selectedCountry]);
        }
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
    }
  };

  const loadAllHolidays = async () => {
    const currentYear = new Date().getFullYear();

    if (selectedCountries.length > 1) {
      await fetchHolidaysForCountries(selectedCountries, {
        year: currentYear,
        limit: 50
      });
    } else {
      await fetchHolidays({
        year: currentYear,
        limit: 50,
        country: selectedCountries[0] || 'Nigeria'
      });
    }
  };

  const filterHolidays = () => {
    let filtered = [...holidays];

    // Filter by type
    if (selectedType !== 'all') {
      filtered = filtered.filter(holiday => holiday.type === selectedType);
    }

    // Filter by time (upcoming vs all)
    if (selectedTimeFilter === 'upcoming') {
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Start of today
      filtered = filtered.filter(holiday => {
        const holidayDate = new Date(holiday.date);
        return holidayDate >= today;
      });
    }

    // Sort by date
    filtered = sortHolidaysByDate(filtered);

    setFilteredHolidays(filtered);
  };

  const handleRefresh = async () => {
    await refreshHolidays();
  };

  const handleLoadMore = async () => {
    await loadMoreHolidays();
  };

  const navigateToHolidayDetail = (holiday: Holiday) => {
    navigation.navigate('HolidayDetail' as never, { holiday } as never);
  };

  const renderTimeFilter = () => {
    const timeFilters = [
      { key: 'upcoming', label: 'Upcoming', color: COLORS.primary[600] },
      { key: 'all', label: 'All This Year', color: COLORS.gray[600] },
    ];

    return (
      <View style={styles.filterContainer}>
        <Text style={styles.filterTitle}>Show Holidays</Text>
        <View style={styles.filterButtons}>
          {timeFilters.map((filter) => (
            <TouchableOpacity
              key={filter.key}
              style={[
                styles.filterButton,
                selectedTimeFilter === filter.key && {
                  backgroundColor: filter.color,
                },
              ]}
              onPress={() => setSelectedTimeFilter(filter.key)}
            >
              <Text
                style={[
                  styles.filterButtonText,
                  selectedTimeFilter === filter.key && styles.filterButtonTextActive,
                ]}
              >
                {filter.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  const renderTypeFilter = () => {
    const types = [
      { key: 'all', label: 'All', color: COLORS.gray[600] },
      ...Object.entries(HOLIDAY_TYPES).map(([key, value]) => ({
        key,
        label: value.label,
        color: value.color,
      })),
    ];

    return (
      <View style={styles.filterContainer}>
        <Text style={styles.filterTitle}>Filter by Type</Text>
        <View style={styles.filterButtons}>
          {types.map((type) => (
            <TouchableOpacity
              key={type.key}
              style={[
                styles.filterButton,
                selectedType === type.key && {
                  backgroundColor: type.color,
                },
              ]}
              onPress={() => setSelectedType(type.key)}
            >
              <Text
                style={[
                  styles.filterButtonText,
                  selectedType === type.key && styles.filterButtonTextActive,
                ]}
              >
                {type.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  const renderHolidayItem = ({ item }: { item: Holiday }) => (
    <HolidayCard
      holiday={item}
      onPress={() => navigateToHolidayDetail(item)}
    />
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="calendar-outline" size={64} color={COLORS.gray[300]} />
      <Text style={styles.emptyTitle}>No Holidays Found</Text>
      <Text style={styles.emptySubtitle}>
        {selectedType === 'all'
          ? 'No holidays available for this year'
          : `No ${selectedType} holidays found`
        }
      </Text>
      <TouchableOpacity
        style={styles.retryButton}
        onPress={handleRefresh}
      >
        <Text style={styles.retryButtonText}>Retry</Text>
      </TouchableOpacity>
    </View>
  );

  const renderHeader = () => (
    <LinearGradient
      colors={[COLORS.primary[600], COLORS.primary[700]]}
      style={styles.header}
    >
      <Text style={styles.headerTitle}>All Holidays</Text>
      <Text style={styles.headerSubtitle}>
        {filteredHolidays.length} holiday{filteredHolidays.length !== 1 ? 's' : ''} found
      </Text>
    </LinearGradient>
  );

  if (loading && !refreshing) {
    return <LoadingSpinner text="Loading holidays..." />;
  }

  return (
    <View style={styles.container}>
      {renderHeader()}

      <FlatList
        data={filteredHolidays}
        renderItem={renderHolidayItem}
        keyExtractor={(item) => item._id}
        ListHeaderComponent={() => (
          <>
            {renderTimeFilter()}
            {renderTypeFilter()}
            <BannerAdComponent />
          </>
        )}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[COLORS.primary[600]]}
            tintColor={COLORS.primary[600]}
          />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.gray[50],
  },
  header: {
    padding: SPACING.lg,
    paddingTop: SPACING.md,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: 'bold',
    color: COLORS.white,
    textAlign: 'center',
    marginBottom: SPACING.xs,
  },
  headerSubtitle: {
    fontSize: FONT_SIZES.md,
    color: COLORS.primary[100],
    textAlign: 'center',
  },
  listContent: {
    paddingBottom: 100, // Extra padding for tab bar and phone navigation
  },
  filterContainer: {
    padding: SPACING.md,
    backgroundColor: COLORS.white,
    marginBottom: SPACING.sm,
  },
  filterTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: 'bold',
    color: COLORS.gray[800],
    marginBottom: SPACING.sm,
  },
  filterButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  filterButton: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: 20,
    backgroundColor: COLORS.gray[100],
    marginRight: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  filterButtonText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: COLORS.white,
    fontWeight: 'bold',
  },
  emptyContainer: {
    alignItems: 'center',
    padding: SPACING.xxl,
    marginTop: SPACING.xl,
  },
  emptyTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: 'bold',
    color: COLORS.gray[600],
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  emptySubtitle: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[500],
    textAlign: 'center',
    marginBottom: SPACING.lg,
  },
  retryButton: {
    backgroundColor: COLORS.primary[600],
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
    borderRadius: 8,
  },
  retryButtonText: {
    color: COLORS.white,
    fontWeight: 'bold',
    fontSize: FONT_SIZES.md,
  },
});

export default HolidaysScreen;
