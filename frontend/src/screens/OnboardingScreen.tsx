import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { COLORS, SPACING, FONT_SIZES, STORAGE_KEYS, COUNTRIES } from '../constants';

interface OnboardingScreenProps {
  onComplete: () => void;
}

const OnboardingScreen: React.FC<OnboardingScreenProps> = ({ onComplete }) => {
  const [selectedCountry, setSelectedCountry] = useState('Nigeria');
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [reminderDays, setReminderDays] = useState([1, 7]); // Multiple reminder days

  const handleCountrySelect = (countryCode: string) => {
    const country = COUNTRIES.find(c => c.code === countryCode);
    if (country) {
      setSelectedCountry(country.name);
    }
  };

  const toggleReminderDay = (day: number) => {
    if (reminderDays.includes(day)) {
      setReminderDays(reminderDays.filter(d => d !== day));
    } else {
      setReminderDays([...reminderDays, day].sort((a, b) => a - b));
    }
  };

  const handleComplete = async () => {
    try {
      const preferences = {
        selectedCountry,
        notifications: notificationsEnabled,
        reminderDays,
        onboardingCompleted: true,
        setupDate: new Date().toISOString(),
      };

      await AsyncStorage.setItem(STORAGE_KEYS.USER_PREFERENCES, JSON.stringify(preferences));
      await AsyncStorage.setItem(STORAGE_KEYS.ONBOARDING_COMPLETED, 'true');
      
      onComplete();
    } catch (error) {
      console.error('Error saving preferences:', error);
      Alert.alert('Error', 'Failed to save preferences. Please try again.');
    }
  };

  const renderCountryOption = (country: any) => (
    <TouchableOpacity
      key={country.code}
      style={[
        styles.countryOption,
        selectedCountry === country.name && styles.selectedCountryOption,
      ]}
      onPress={() => handleCountrySelect(country.code)}
    >
      <Text style={styles.countryFlag}>{country.flag}</Text>
      <Text
        style={[
          styles.countryName,
          selectedCountry === country.name && styles.selectedCountryName,
        ]}
      >
        {country.name}
      </Text>
      {selectedCountry === country.name && (
        <Ionicons name="checkmark-circle" size={20} color={COLORS.primary[600]} />
      )}
    </TouchableOpacity>
  );

  const renderReminderOption = (day: number, label: string) => (
    <TouchableOpacity
      key={day}
      style={[
        styles.reminderOption,
        reminderDays.includes(day) && styles.selectedReminderOption,
      ]}
      onPress={() => toggleReminderDay(day)}
    >
      <Text
        style={[
          styles.reminderText,
          reminderDays.includes(day) && styles.selectedReminderText,
        ]}
      >
        {label}
      </Text>
      {reminderDays.includes(day) && (
        <Ionicons name="checkmark" size={16} color={COLORS.white} />
      )}
    </TouchableOpacity>
  );

  return (
    <LinearGradient
      colors={[COLORS.primary[600], COLORS.primary[700]]}
      style={styles.container}
    >
      <ScrollView contentContainerStyle={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.logoContainer}>
            <Ionicons name="calendar" size={60} color={COLORS.white} />
          </View>
          <Text style={styles.title}>Welcome to Holivia!</Text>
          <Text style={styles.subtitle}>
            Let's set up your holiday preferences
          </Text>
        </View>

        {/* Country Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Select Your Country</Text>
          <Text style={styles.sectionDescription}>
            Choose your country to see relevant holidays
          </Text>
          <View style={styles.countryGrid}>
            {COUNTRIES.map(renderCountryOption)}
          </View>
        </View>

        {/* Notification Preferences */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notification Preferences</Text>
          <TouchableOpacity
            style={styles.notificationToggle}
            onPress={() => setNotificationsEnabled(!notificationsEnabled)}
          >
            <View style={styles.toggleLeft}>
              <Ionicons 
                name={notificationsEnabled ? "notifications" : "notifications-off"} 
                size={24} 
                color={COLORS.white} 
              />
              <Text style={styles.toggleText}>Enable Notifications</Text>
            </View>
            <View
              style={[
                styles.toggleSwitch,
                notificationsEnabled && styles.toggleSwitchActive,
              ]}
            >
              <View
                style={[
                  styles.toggleThumb,
                  notificationsEnabled && styles.toggleThumbActive,
                ]}
              />
            </View>
          </TouchableOpacity>
        </View>

        {/* Reminder Days */}
        {notificationsEnabled && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Reminder Days</Text>
            <Text style={styles.sectionDescription}>
              Choose when to be reminded before holidays (you can select multiple)
            </Text>
            <View style={styles.reminderGrid}>
              {renderReminderOption(0, 'Same Day')}
              {renderReminderOption(1, '1 Day Before')}
              {renderReminderOption(3, '3 Days Before')}
              {renderReminderOption(7, '1 Week Before')}
              {renderReminderOption(14, '2 Weeks Before')}
              {renderReminderOption(30, '1 Month Before')}
            </View>
          </View>
        )}

        {/* Complete Button */}
        <TouchableOpacity style={styles.completeButton} onPress={handleComplete}>
          <Text style={styles.completeButtonText}>Get Started</Text>
          <Ionicons name="arrow-forward" size={20} color={COLORS.primary[600]} />
        </TouchableOpacity>
      </ScrollView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: SPACING.lg,
    paddingBottom: SPACING.xxl,
  },
  header: {
    alignItems: 'center',
    marginBottom: SPACING.xxl,
    paddingTop: SPACING.xl,
  },
  logoContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(255,255,255,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  title: {
    fontSize: FONT_SIZES.xxxl,
    fontWeight: 'bold',
    color: COLORS.white,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: FONT_SIZES.lg,
    color: COLORS.primary[100],
    textAlign: 'center',
  },
  section: {
    marginBottom: SPACING.xl,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: 'bold',
    color: COLORS.white,
    marginBottom: SPACING.sm,
  },
  sectionDescription: {
    fontSize: FONT_SIZES.md,
    color: COLORS.primary[100],
    marginBottom: SPACING.md,
    lineHeight: 20,
  },
  countryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  countryOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.1)',
    padding: SPACING.md,
    borderRadius: 12,
    marginBottom: SPACING.sm,
    width: '48%',
  },
  selectedCountryOption: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderWidth: 2,
    borderColor: COLORS.white,
  },
  countryFlag: {
    fontSize: 24,
    marginRight: SPACING.sm,
  },
  countryName: {
    flex: 1,
    fontSize: FONT_SIZES.md,
    color: COLORS.white,
    fontWeight: '500',
  },
  selectedCountryName: {
    fontWeight: 'bold',
  },
  notificationToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'rgba(255,255,255,0.1)',
    padding: SPACING.md,
    borderRadius: 12,
  },
  toggleLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  toggleText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.white,
    marginLeft: SPACING.sm,
    fontWeight: '500',
  },
  toggleSwitch: {
    width: 50,
    height: 26,
    borderRadius: 13,
    backgroundColor: 'rgba(255,255,255,0.3)',
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  toggleSwitchActive: {
    backgroundColor: COLORS.white,
  },
  toggleThumb: {
    width: 22,
    height: 22,
    borderRadius: 11,
    backgroundColor: COLORS.white,
  },
  toggleThumbActive: {
    backgroundColor: COLORS.primary[600],
    alignSelf: 'flex-end',
  },
  reminderGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  reminderOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.1)',
    padding: SPACING.sm,
    borderRadius: 8,
    marginBottom: SPACING.sm,
    minWidth: '30%',
    justifyContent: 'center',
  },
  selectedReminderOption: {
    backgroundColor: COLORS.white,
  },
  reminderText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.white,
    fontWeight: '500',
    marginRight: SPACING.xs,
  },
  selectedReminderText: {
    color: COLORS.primary[600],
    fontWeight: 'bold',
  },
  completeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.white,
    padding: SPACING.lg,
    borderRadius: 12,
    marginTop: SPACING.xl,
  },
  completeButtonText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: 'bold',
    color: COLORS.primary[600],
    marginRight: SPACING.sm,
  },
});

export default OnboardingScreen;
