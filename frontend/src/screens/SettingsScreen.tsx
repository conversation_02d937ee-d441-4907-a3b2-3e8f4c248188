import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  Linking,
  Modal,
  FlatList,
  Pressable,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { COLORS, SPACING, FONT_SIZES, STORAGE_KEYS, COUNTRIES } from '../constants';
import notificationService from '../services/notificationService';
import adService from '../services/adService';
import cacheService from '../services/cacheService';
import apiService from '../services/api';

interface SettingsState {
  notifications: boolean;
  reminderDays: number[];
  selectedCountry: string;
  selectedCountries: string[]; // Support multiple countries
  darkMode: boolean;
  autoRefresh: boolean;
  cacheSize: number;
  lastSync: string;
}

interface Country {
  code: string;
  name: string;
  flag: string;
}

const SettingsScreen: React.FC = () => {
  const [settings, setSettings] = useState<SettingsState>({
    notifications: true,
    reminderDays: [1, 7],
    selectedCountry: 'Nigeria',
    selectedCountries: ['Nigeria'], // Initialize with default country
    darkMode: false,
    autoRefresh: true,
    cacheSize: 0,
    lastSync: 'Never',
  });

  const [availableCountries, setAvailableCountries] = useState<Country[]>(COUNTRIES);
  const [showCountryModal, setShowCountryModal] = useState(false);
  const [showReminderModal, setShowReminderModal] = useState(false);
  const [tempSelectedCountries, setTempSelectedCountries] = useState<string[]>([]);

  const reminderOptions = [
    { value: 0, label: 'Same Day', description: 'On the holiday' },
    { value: 1, label: '1 Day Before', description: 'Day before the holiday' },
    { value: 3, label: '3 Days Before', description: '3 days before the holiday' },
    { value: 7, label: '1 Week Before', description: 'A week before the holiday' },
    { value: 14, label: '2 Weeks Before', description: 'Two weeks before the holiday' },
    { value: 30, label: '1 Month Before', description: 'A month before the holiday' },
  ];

  useEffect(() => {
    loadSettings();
    loadAvailableCountries();

    // Show rewarded ad for accessing settings (occasionally)
    setTimeout(() => {
      if (Math.random() < 0.3 && adService.isRewardedAdReady()) { // 30% chance
        adService.showRewardedAdWithCallback((reward) => {
          console.log('🎉 User earned reward for accessing settings:', reward);
        });
      }
    }, 2000); // Delay to let screen load
  }, []);

  const loadSettings = async () => {
    try {
      const savedSettings = await AsyncStorage.getItem(STORAGE_KEYS.USER_PREFERENCES);
      const notificationPrefs = await notificationService.getPreferences();
      const cacheSize = await cacheService.getCacheSizeInMB();
      const lastSyncTime = await cacheService.getLastSyncTime();

      let loadedSettings = {
        notifications: notificationPrefs.enabled,
        reminderDays: notificationPrefs.reminderDays,
        selectedCountry: 'Nigeria',
        selectedCountries: ['Nigeria'], // Default to Nigeria
        darkMode: false,
        autoRefresh: true,
        cacheSize: Math.round(cacheSize * 100) / 100, // Round to 2 decimal places
        lastSync: lastSyncTime ? new Date(lastSyncTime).toLocaleDateString() : 'Never',
      };

      if (savedSettings) {
        const parsed = JSON.parse(savedSettings);
        loadedSettings = { ...loadedSettings, ...parsed };
        // Ensure selectedCountries is always an array
        if (!Array.isArray(loadedSettings.selectedCountries)) {
          loadedSettings.selectedCountries = [loadedSettings.selectedCountry || 'Nigeria'];
        }
      }

      setSettings(loadedSettings);
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const loadAvailableCountries = async () => {
    try {
      const response = await apiService.getCountries();
      if (response.success && response.data) {
        const countries = response.data.map(countryName => {
          const existing = COUNTRIES.find(c => c.code === countryName);
          return existing || { code: countryName, name: countryName, flag: '🌍' };
        });
        setAvailableCountries(countries);
      }
    } catch (error) {
      console.error('Error loading countries:', error);
      // Fallback to static countries
      setAvailableCountries(COUNTRIES);
    }
  };

  const saveSettings = async (newSettings: SettingsState, skipNotificationReschedule = false) => {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.USER_PREFERENCES, JSON.stringify(newSettings));

      // Update notification preferences
      await notificationService.savePreferences({
        enabled: newSettings.notifications,
        reminderDays: newSettings.reminderDays,
        sound: true,
        vibration: true,
      });

      // Only schedule notifications if user has enabled them and granted permissions
      if (newSettings.notifications) {
        const hasPermissions = await notificationService.hasPermissions();
        if (hasPermissions) {
          // Reschedule month-end notifications with new preferences
          await notificationService.scheduleMonthEndNotifications();

          // If countries were updated and we're not skipping notification reschedule
          if (!skipNotificationReschedule && newSettings.selectedCountries && newSettings.selectedCountries.length > 0) {
            await notificationService.scheduleNotificationsForCountries(newSettings.selectedCountries);
          }
        } else {
          // Request permissions if notifications are enabled but not granted
          await notificationService.requestPermissions();
        }
      } else {
        // Cancel all notifications if disabled
        await notificationService.cancelAllNotifications();
      }

      setSettings(newSettings);
    } catch (error) {
      console.error('Error saving settings:', error);
      Alert.alert('Error', 'Failed to save settings');
    }
  };

  const saveSettingsWithoutNotifications = async (newSettings: SettingsState) => {
    await saveSettings(newSettings, true);
  };

  const updateSetting = (key: keyof SettingsState, value: any) => {
    const newSettings = { ...settings, [key]: value };
    saveSettings(newSettings);
  };

  const toggleReminderDay = (day: number) => {
    const currentDays = [...settings.reminderDays];
    const index = currentDays.indexOf(day);

    if (index > -1) {
      currentDays.splice(index, 1);
    } else {
      currentDays.push(day);
      currentDays.sort((a, b) => a - b);
    }

    updateSetting('reminderDays', currentDays);
  };



  const clearCache = async () => {
    Alert.alert(
      'Clear Cache',
      `This will remove all cached holiday data (${settings.cacheSize} MB). Are you sure?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              await cacheService.clearAll();
              await loadSettings(); // Reload to update cache size
              Alert.alert('Success', 'Cache cleared successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to clear cache');
            }
          },
        },
      ]
    );
  };

  const sendFeedback = () => {
    Linking.openURL('mailto:<EMAIL>?subject=Holivia App Feedback');
  };

  const renderSettingItem = (
    icon: string,
    title: string,
    subtitle?: string,
    rightComponent?: React.ReactNode,
    onPress?: () => void
  ) => (
    <TouchableOpacity
      style={styles.settingItem}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.settingLeft}>
        <View style={styles.iconContainer}>
          <Ionicons name={icon as any} size={20} color={COLORS.primary[600]} />
        </View>
        <View style={styles.settingText}>
          <Text style={styles.settingTitle}>{title}</Text>
          {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      {rightComponent && <View style={styles.settingRight}>{rightComponent}</View>}
      {onPress && !rightComponent && (
        <Ionicons name="chevron-forward" size={20} color={COLORS.gray[400]} />
      )}
    </TouchableOpacity>
  );

  const renderSection = (title: string, children: React.ReactNode) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.sectionContent}>{children}</View>
    </View>
  );

  const toggleCountrySelection = (countryCode: string) => {
    const currentCountries = [...tempSelectedCountries];
    const index = currentCountries.indexOf(countryCode);

    if (index > -1) {
      // Remove country if already selected
      currentCountries.splice(index, 1);

      // If no countries left, add Nigeria as default
      if (currentCountries.length === 0) {
        currentCountries.push('Nigeria');
      }
    } else {
      // Add country if not selected
      currentCountries.push(countryCode);
    }

    // Update temporary state only
    setTempSelectedCountries(currentCountries);
  };

  const openCountryModal = () => {
    setTempSelectedCountries([...settings.selectedCountries]);
    setShowCountryModal(true);
  };

  const closeCountryModal = async () => {
    // Save the final selection when modal is closed
    const newSettings = {
      ...settings,
      selectedCountries: tempSelectedCountries,
      selectedCountry: tempSelectedCountries[0]
    };
    await saveSettings(newSettings);
    setShowCountryModal(false);
  };

  const renderCountryModal = () => (
    <Modal
      visible={showCountryModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowCountryModal(false)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>Select Countries</Text>
          <TouchableOpacity
            onPress={() => setShowCountryModal(false)}
            style={styles.modalCloseButton}
          >
            <Ionicons name="close" size={24} color={COLORS.gray[600]} />
          </TouchableOpacity>
        </View>
        <Text style={styles.modalSubtitle}>
          Select multiple countries to receive holiday notifications from all of them.
        </Text>

        <FlatList
          data={availableCountries}
          keyExtractor={(item) => item.code}
          renderItem={({ item }) => {
            const isSelected = tempSelectedCountries.includes(item.code);
            return (
              <TouchableOpacity
                style={[
                  styles.countryItem,
                  isSelected && styles.countryItemSelected
                ]}
                onPress={() => toggleCountrySelection(item.code)}
              >
                <Text style={styles.countryFlag}>{item.flag}</Text>
                <Text style={styles.countryName}>{item.name}</Text>
                {isSelected && (
                  <Ionicons name="checkmark" size={20} color={COLORS.primary[600]} />
                )}
              </TouchableOpacity>
            );
          }}
          showsVerticalScrollIndicator={false}
        />
        <View style={styles.modalFooter}>
          <TouchableOpacity
            style={styles.modalDoneButton}
            onPress={closeCountryModal}
          >
            <Text style={styles.modalDoneButtonText}>
              Done ({tempSelectedCountries.length} selected)
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  const renderReminderModal = () => (
    <Modal
      visible={showReminderModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowReminderModal(false)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>Reminder Days</Text>
          <TouchableOpacity
            onPress={() => setShowReminderModal(false)}
            style={styles.modalCloseButton}
          >
            <Ionicons name="close" size={24} color={COLORS.gray[600]} />
          </TouchableOpacity>
        </View>

        <Text style={styles.modalSubtitle}>
          Choose when to be reminded before holidays (you can select multiple)
        </Text>

        <FlatList
          data={reminderOptions}
          keyExtractor={(item) => item.value.toString()}
          renderItem={({ item }) => {
            const isSelected = settings.reminderDays.includes(item.value);
            return (
              <TouchableOpacity
                style={[
                  styles.reminderItem,
                  isSelected && styles.reminderItemSelected
                ]}
                onPress={() => toggleReminderDay(item.value)}
              >
                <View style={styles.reminderItemLeft}>
                  <View style={[
                    styles.reminderCheckbox,
                    isSelected && styles.reminderCheckboxSelected
                  ]}>
                    {isSelected && (
                      <Ionicons name="checkmark" size={16} color={COLORS.white} />
                    )}
                  </View>
                  <View style={styles.reminderText}>
                    <Text style={styles.reminderLabel}>{item.label}</Text>
                    <Text style={styles.reminderDescription}>{item.description}</Text>
                  </View>
                </View>
              </TouchableOpacity>
            );
          }}
          showsVerticalScrollIndicator={false}
        />

        <View style={styles.modalFooter}>
          <TouchableOpacity
            style={styles.modalDoneButton}
            onPress={() => setShowReminderModal(false)}
          >
            <Text style={styles.modalDoneButtonText}>Done</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={styles.container}>
      {renderCountryModal()}
      {renderReminderModal()}

      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
      {/* Header */}
      <LinearGradient
        colors={[COLORS.primary[600], COLORS.primary[700]]}
        style={styles.header}
      >
        <Text style={styles.headerTitle}>Settings</Text>
        <Text style={styles.headerSubtitle}>
          Customize your Holivia experience
        </Text>
      </LinearGradient>

      {/* Notifications Section */}
      {renderSection(
        'Notifications',
        <>
          {renderSettingItem(
            'notifications',
            'Push Notifications',
            'Receive holiday reminders',
            <Switch
              value={settings.notifications}
              onValueChange={(value) => updateSetting('notifications', value)}
              trackColor={{ false: COLORS.gray[300], true: COLORS.primary[200] }}
              thumbColor={settings.notifications ? COLORS.primary[600] : COLORS.gray[400]}
            />
          )}
          {renderSettingItem(
            'time',
            'Reminder Days',
            `Active: ${settings.reminderDays.length > 0 ? settings.reminderDays.join(', ') + ' day(s) before' : 'None'}`,
            undefined,
            () => setShowReminderModal(true)
          )}
        </>
      )}

      {/* Preferences Section */}
      {renderSection(
        'Preferences',
        <>
          {renderSettingItem(
            'globe',
            'Countries',
            `Selected: ${settings.selectedCountries.length > 1
              ? `${settings.selectedCountries.length} countries`
              : settings.selectedCountries[0] || 'None'}`,
            undefined,
            openCountryModal
          )}
          {renderSettingItem(
            'refresh',
            'Auto Refresh',
            'Automatically refresh holiday data',
            <Switch
              value={settings.autoRefresh}
              onValueChange={(value) => updateSetting('autoRefresh', value)}
              trackColor={{ false: COLORS.gray[300], true: COLORS.primary[200] }}
              thumbColor={settings.autoRefresh ? COLORS.primary[600] : COLORS.gray[400]}
            />
          )}
        </>
      )}

      {/* Data Section */}
      {renderSection(
        'Data & Cache',
        <>
          {renderSettingItem(
            'server',
            'Cache Size',
            `${settings.cacheSize} MB of cached data`,
            undefined
          )}
          {renderSettingItem(
            'time',
            'Last Sync',
            settings.lastSync,
            undefined
          )}
          {renderSettingItem(
            'trash',
            'Clear Cache',
            'Remove all cached holiday data',
            undefined,
            clearCache
          )}
        </>
      )}

      {/* Support Section */}
      {renderSection(
        'Support',
        <>
          {renderSettingItem(
            'mail',
            'Send Feedback',
            'Help us improve Holivia',
            undefined,
            sendFeedback
          )}
        </>
      )}

      {/* Developer Credit Section */}
      {renderSection(
        'About',
        <>
          <View style={styles.developerCredit}>
            <Text style={styles.appName}>Holivia</Text>
            <Text style={styles.appVersion}>Version 1.0.0</Text>
            <Text style={styles.appDescription}>
              Your personal holiday reminder app
            </Text>
            <Text style={styles.developerText}>
              Developed by EmmzyTech
            </Text>
          </View>
        </>
      )}


      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.gray[50],
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 100, // Extra padding for tab bar and phone navigation
  },
  header: {
    padding: SPACING.lg,
    paddingTop: SPACING.md,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: 'bold',
    color: COLORS.white,
    textAlign: 'center',
    marginBottom: SPACING.sm,
    marginTop: SPACING.sm,
  },
  headerSubtitle: {
    fontSize: FONT_SIZES.md,
    color: COLORS.primary[100],
    textAlign: 'center',
  },
  section: {
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: 'bold',
    color: COLORS.gray[600],
    marginHorizontal: SPACING.md,
    marginBottom: SPACING.sm,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  sectionContent: {
    backgroundColor: COLORS.white,
    borderRadius: 16,
    marginHorizontal: SPACING.md,
    overflow: 'hidden',
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[50],
    minHeight: 72,
  },
  settingLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    backgroundColor: COLORS.primary[50],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.lg,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '500',
    color: COLORS.gray[800],
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[500],
  },
  settingRight: {
    marginLeft: SPACING.md,
  },
  reminderButton: {
    backgroundColor: COLORS.primary[100],
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: 8,
    minWidth: 40,
    alignItems: 'center',
  },
  reminderButtonText: {
    fontSize: FONT_SIZES.sm,
    fontWeight: 'bold',
    color: COLORS.primary[700],
  },

  appName: {
    fontSize: FONT_SIZES.xl,
    fontWeight: 'bold',
    color: COLORS.primary[600],
    marginBottom: SPACING.xs,
  },
  appVersion: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[500],
    marginBottom: SPACING.sm,
  },
  appDescription: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: SPACING.md,
  },
  developerCredit: {
    alignItems: 'center',
    padding: SPACING.lg,
    backgroundColor: COLORS.white,
  },
  developerText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primary[600],
    fontWeight: '600',
    textAlign: 'center',
  },
  // Modal Styles
  modalContainer: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.lg,
    paddingTop: SPACING.xl,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[100],
  },
  modalTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: 'bold',
    color: COLORS.gray[800],
  },
  modalSubtitle: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[600],
    padding: SPACING.lg,
    paddingBottom: SPACING.md,
    lineHeight: 22,
  },
  modalCloseButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalFooter: {
    padding: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray[100],
  },
  modalDoneButton: {
    backgroundColor: COLORS.primary[600],
    paddingVertical: SPACING.md,
    borderRadius: 12,
    alignItems: 'center',
  },
  modalDoneButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.white,
  },
  // Country Modal Styles
  countryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[50],
  },
  countryItemSelected: {
    backgroundColor: COLORS.primary[25],
  },
  countryFlag: {
    fontSize: 24,
    marginRight: SPACING.md,
  },
  countryName: {
    flex: 1,
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[800],
    fontWeight: '500',
  },
  // Reminder Modal Styles
  reminderItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[50],
  },
  reminderItemSelected: {
    backgroundColor: COLORS.primary[25],
  },
  reminderItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  reminderCheckbox: {
    width: 24,
    height: 24,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: COLORS.gray[300],
    marginRight: SPACING.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  reminderCheckboxSelected: {
    backgroundColor: COLORS.primary[600],
    borderColor: COLORS.primary[600],
  },
  reminderText: {
    flex: 1,
  },
  reminderLabel: {
    fontSize: FONT_SIZES.md,
    fontWeight: '500',
    color: COLORS.gray[800],
    marginBottom: 2,
  },
  reminderDescription: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[500],
  },
});

export default SettingsScreen;
