import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  FlatList,
  TouchableOpacity,
  Alert,
  Keyboard,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Holiday } from '../types';
import { COLORS, SPACING, FONT_SIZES, STORAGE_KEYS } from '../constants';
import { useHolidays } from '../hooks/useHolidays';
import { debounce } from '../utils';
import LoadingSpinner from '../components/LoadingSpinner';
import HolidayCard from '../components/HolidayCard';

const SearchScreen: React.FC = () => {
  const navigation = useNavigation();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Holiday[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [selectedCountries, setSelectedCountries] = useState<string[]>(['Nigeria']);

  const {
    holidays,
    searchHolidays,
    searchHolidaysForCountries,
    clearError,
    error,
    loading,
  } = useHolidays();

  useEffect(() => {
    loadUserPreferences();
  }, []);

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error, [
        { text: 'OK', onPress: clearError },
      ]);
    }
  }, [error, clearError]);

  // Update search results when holidays change (from search)
  useEffect(() => {
    if (hasSearched && !loading) {
      setSearchResults(holidays);
      setIsSearching(false);
    }
  }, [holidays, hasSearched, loading]);

  const loadUserPreferences = async () => {
    try {
      const savedSettings = await AsyncStorage.getItem(STORAGE_KEYS.USER_PREFERENCES);
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings);
        if (parsed.selectedCountries && Array.isArray(parsed.selectedCountries)) {
          setSelectedCountries(parsed.selectedCountries);
        } else if (parsed.selectedCountry) {
          setSelectedCountries([parsed.selectedCountry]);
        }
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
    }
  };

  const performSearch = useCallback(async (query: string) => {
    if (query.trim().length < 2) {
      setSearchResults([]);
      setHasSearched(false);
      return;
    }

    setIsSearching(true);
    setHasSearched(true);

    try {
      if (selectedCountries.length > 1) {
        await searchHolidaysForCountries(query.trim(), selectedCountries);
      } else {
        await searchHolidays(query.trim(), selectedCountries[0] || 'Nigeria');
      }
      // searchHolidays updates the holidays state with search results
      setSearchResults(holidays);
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, [searchHolidays, searchHolidaysForCountries, selectedCountries, holidays]);

  const debouncedSearch = useCallback(
    debounce((query: string) => performSearch(query), 500),
    [performSearch]
  );

  const handleSearchChange = (text: string) => {
    setSearchQuery(text);
    debouncedSearch(text);
  };

  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
    setHasSearched(false);
    Keyboard.dismiss();
  };

  const navigateToHolidayDetail = (holiday: Holiday) => {
    navigation.navigate('HolidayDetail' as never, { holiday } as never);
  };

  const renderSearchBar = () => (
    <View style={styles.searchContainer}>
      <View style={styles.searchInputContainer}>
        <Ionicons
          name="search"
          size={20}
          color={COLORS.gray[400]}
          style={styles.searchIcon}
        />
        <TextInput
          style={styles.searchInput}
          placeholder="Search holidays..."
          placeholderTextColor={COLORS.gray[400]}
          value={searchQuery}
          onChangeText={handleSearchChange}
          autoCapitalize="none"
          autoCorrect={false}
          returnKeyType="search"
          onSubmitEditing={() => performSearch(searchQuery)}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={clearSearch} style={styles.clearButton}>
            <Ionicons name="close-circle" size={20} color={COLORS.gray[400]} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const renderSearchSuggestions = () => (
    <View style={styles.suggestionsContainer}>
      <Text style={styles.suggestionsTitle}>Popular Searches</Text>
      <View style={styles.suggestionTags}>
        {['Christmas', 'New Year', 'Independence', 'Religious', 'National'].map((suggestion) => (
          <TouchableOpacity
            key={suggestion}
            style={styles.suggestionTag}
            onPress={() => handleSearchChange(suggestion)}
          >
            <Text style={styles.suggestionText}>{suggestion}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderHolidayItem = ({ item }: { item: Holiday }) => (
    <HolidayCard
      holiday={item}
      onPress={() => navigateToHolidayDetail(item)}
      searchQuery={searchQuery}
    />
  );

  const renderEmptyState = () => {
    if (!hasSearched) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="search-outline" size={64} color={COLORS.gray[300]} />
          <Text style={styles.emptyTitle}>Search for Holidays</Text>
          <Text style={styles.emptySubtitle}>
            Enter a holiday name, type, or description to find holidays
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <Ionicons name="calendar-outline" size={64} color={COLORS.gray[300]} />
        <Text style={styles.emptyTitle}>No Results Found</Text>
        <Text style={styles.emptySubtitle}>
          Try searching with different keywords or check your spelling
        </Text>
      </View>
    );
  };

  const renderSearchResults = () => {
    if (isSearching) {
      return (
        <View style={styles.loadingContainer}>
          <LoadingSpinner size="small" text="Searching..." />
        </View>
      );
    }

    return (
      <FlatList
        data={searchResults}
        renderItem={renderHolidayItem}
        keyExtractor={(item) => item._id}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.resultsContent}
      />
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={[COLORS.primary[600], COLORS.primary[700]]}
        style={styles.header}
      >
        <Text style={styles.headerTitle}>Search Holidays</Text>
        <Text style={styles.headerSubtitle}>
          Find holidays by name, type, or description
        </Text>
      </LinearGradient>

      {/* Search Bar */}
      {renderSearchBar()}

      {/* Content */}
      {!hasSearched && !isSearching ? (
        <>
          {renderSearchSuggestions()}
          {renderEmptyState()}
        </>
      ) : (
        renderSearchResults()
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.gray[50],
  },
  header: {
    padding: SPACING.lg,
    paddingTop: SPACING.md,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: 'bold',
    color: COLORS.white,
    textAlign: 'center',
    marginBottom: SPACING.xs,
  },
  headerSubtitle: {
    fontSize: FONT_SIZES.md,
    color: COLORS.primary[100],
    textAlign: 'center',
  },
  searchContainer: {
    padding: SPACING.md,
    backgroundColor: COLORS.white,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.gray[100],
    borderRadius: 12,
    paddingHorizontal: SPACING.md,
  },
  searchIcon: {
    marginRight: SPACING.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[800],
    paddingVertical: SPACING.md,
  },
  clearButton: {
    padding: SPACING.xs,
  },
  suggestionsContainer: {
    padding: SPACING.md,
    backgroundColor: COLORS.white,
    marginBottom: SPACING.sm,
  },
  suggestionsTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: 'bold',
    color: COLORS.gray[800],
    marginBottom: SPACING.sm,
  },
  suggestionTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  suggestionTag: {
    backgroundColor: COLORS.primary[100],
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: 20,
    marginRight: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  suggestionText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primary[700],
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
  },
  resultsContent: {
    paddingBottom: 100, // Extra padding for tab bar and phone navigation
  },
  emptyContainer: {
    alignItems: 'center',
    padding: SPACING.xxl,
    marginTop: SPACING.xl,
  },
  emptyTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: 'bold',
    color: COLORS.gray[600],
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  emptySubtitle: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[500],
    textAlign: 'center',
    lineHeight: 22,
  },
});

export default SearchScreen;
