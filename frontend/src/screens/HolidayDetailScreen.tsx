import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Share,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Holiday } from '../types';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../constants';
import {
  formatDateLong,
  getRelativeDateDescription,
  getHolidayTypeInfo,
  capitalizeWords
} from '../utils';
import BannerAdComponent from '../components/BannerAdComponent';
import adService from '../services/adService';

interface HolidayDetailRouteParams {
  holiday: Holiday;
}

const HolidayDetailScreen: React.FC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { holiday } = route.params as HolidayDetailRouteParams;

  const typeInfo = getHolidayTypeInfo(holiday.type);
  const relativeDate = getRelativeDateDescription(holiday.date);

  useEffect(() => {
    // Show interstitial ad if conditions are met
    if (adService.shouldShowInterstitialAd()) {
      setTimeout(() => {
        adService.showInterstitialAd();
      }, 1000); // Delay to let screen load
    }
  }, []);

  const handleShare = async () => {
    try {
      const message = `🎉 ${holiday.name}\n📅 ${formatDateLong(holiday.date)}\n\n${holiday.description}\n\nShared from Holivia App`;

      await Share.share({
        message,
        title: holiday.name,
      });
    } catch (error) {
      console.error('Error sharing:', error);
      Alert.alert('Error', 'Failed to share holiday information');
    }
  };

  const renderInfoSection = (title: string, content: string | string[], icon: string) => {
    if (!content || (Array.isArray(content) && content.length === 0)) return null;

    return (
      <View style={styles.infoSection}>
        <View style={styles.sectionHeader}>
          <Ionicons name={icon as any} size={20} color={typeInfo.color} />
          <Text style={styles.sectionTitle}>{title}</Text>
        </View>
        {Array.isArray(content) ? (
          content.map((item, index) => (
            <View key={index} style={styles.listItem}>
              <View style={[styles.bullet, { backgroundColor: typeInfo.color }]} />
              <Text style={styles.listText}>{item}</Text>
            </View>
          ))
        ) : (
          <Text style={styles.sectionContent}>{content}</Text>
        )}
      </View>
    );
  };

  return (
    <ScrollView style={styles.container}>
      {/* Hero Section */}
      <LinearGradient
        colors={[typeInfo.color, typeInfo.color + 'CC']}
        style={styles.heroSection}
      >
        <View style={styles.heroContent}>
          <View style={styles.heroHeader}>
            <View style={[styles.typeBadge, { backgroundColor: 'rgba(255,255,255,0.2)' }]}>
              <Text style={styles.typeText}>{typeInfo.label}</Text>
            </View>
            <TouchableOpacity onPress={handleShare} style={styles.shareButton}>
              <Ionicons name="share-outline" size={24} color={COLORS.white} />
            </TouchableOpacity>
          </View>

          <Text style={styles.holidayName}>{holiday.name}</Text>

          <View style={styles.dateContainer}>
            <Ionicons name="calendar" size={20} color={COLORS.white} />
            <Text style={styles.dateText}>{formatDateLong(holiday.date)}</Text>
          </View>

          <Text style={styles.relativeDateText}>{relativeDate}</Text>
        </View>
      </LinearGradient>

      {/* Content Sections */}
      <View style={styles.contentContainer}>
        {/* Description */}
        {renderInfoSection('About', holiday.description, 'information-circle')}

        {/* History */}
        {holiday.history && renderInfoSection('History', holiday.history, 'library')}

        {/* Activities */}
        {holiday.activities && holiday.activities.length > 0 &&
          renderInfoSection('Traditional Activities', holiday.activities, 'list')}

        {/* Holiday Details */}
        <View style={styles.infoSection}>
          <View style={styles.sectionHeader}>
            <Ionicons name="settings" size={20} color={typeInfo.color} />
            <Text style={styles.sectionTitle}>Holiday Information</Text>
          </View>

          <View style={styles.detailsGrid}>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Country</Text>
              <Text style={styles.detailValue}>{holiday.country}</Text>
            </View>

            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Year</Text>
              <Text style={styles.detailValue}>{holiday.year}</Text>
            </View>

            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Type</Text>
              <Text style={styles.detailValue}>{capitalizeWords(holiday.type)}</Text>
            </View>

            {holiday.isRecurring && (
              <View style={styles.detailItem}>
                <Text style={styles.detailLabel}>Recurring</Text>
                <Text style={styles.detailValue}>
                  {capitalizeWords(holiday.recurringPattern || 'yearly')}
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Banner Ad */}
        <BannerAdComponent style={{ marginHorizontal: SPACING.lg }} />

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: typeInfo.color }]}
            onPress={handleShare}
          >
            <Ionicons name="share" size={20} color={COLORS.white} />
            <Text style={styles.actionButtonText}>Share Holiday</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryButton]}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={20} color={typeInfo.color} />
            <Text style={[styles.actionButtonText, { color: typeInfo.color }]}>
              Back to List
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.gray[50],
  },
  heroSection: {
    paddingTop: SPACING.xl,
    paddingBottom: SPACING.xxl,
    paddingHorizontal: SPACING.lg,
  },
  heroContent: {
    alignItems: 'center',
  },
  heroHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: SPACING.lg,
  },
  typeBadge: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.full,
  },
  typeText: {
    fontSize: FONT_SIZES.sm,
    fontWeight: 'bold',
    color: COLORS.white,
    textTransform: 'uppercase',
  },
  shareButton: {
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  holidayName: {
    fontSize: FONT_SIZES.xxxl,
    fontWeight: 'bold',
    color: COLORS.white,
    textAlign: 'center',
    marginBottom: SPACING.lg,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  dateText: {
    fontSize: FONT_SIZES.lg,
    color: COLORS.white,
    marginLeft: SPACING.sm,
    fontWeight: '500',
  },
  relativeDateText: {
    fontSize: FONT_SIZES.md,
    color: 'rgba(255,255,255,0.9)',
    fontWeight: '600',
  },
  contentContainer: {
    padding: SPACING.lg,
  },
  infoSection: {
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
    marginBottom: SPACING.md,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: 'bold',
    color: COLORS.gray[800],
    marginLeft: SPACING.sm,
  },
  sectionContent: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[600],
    lineHeight: 24,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: SPACING.sm,
  },
  bullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginTop: 8,
    marginRight: SPACING.sm,
  },
  listText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[600],
    lineHeight: 22,
    flex: 1,
  },
  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  detailItem: {
    width: '48%',
    marginBottom: SPACING.md,
  },
  detailLabel: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[500],
    marginBottom: SPACING.xs,
    fontWeight: '500',
  },
  detailValue: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[800],
    fontWeight: '600',
  },
  actionButtons: {
    marginTop: SPACING.lg,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
    marginBottom: SPACING.sm,
  },
  secondaryButton: {
    backgroundColor: COLORS.white,
    borderWidth: 2,
    borderColor: COLORS.primary[600],
  },
  actionButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: 'bold',
    color: COLORS.white,
    marginLeft: SPACING.sm,
  },
});

export default HolidayDetailScreen;
