import { useState, useEffect, useCallback } from 'react';
import { Holiday, HolidayFilters, HolidayResponse } from '../types';
import { apiService } from '../services/api';
import { APP_CONFIG } from '../constants';

interface UseHolidaysState {
  holidays: Holiday[];
  loading: boolean;
  error: string | null;
  pagination: HolidayResponse['pagination'];
  refreshing: boolean;
}

interface UseHolidaysReturn extends UseHolidaysState {
  fetchHolidays: (filters?: HolidayFilters) => Promise<void>;
  fetchUpcomingHolidays: (country?: string, limit?: number) => Promise<void>;
  fetchUpcomingHolidaysForCountries: (countries: string[], limit?: number) => Promise<void>;
  searchHolidays: (query: string, country?: string) => Promise<void>;
  refreshHolidays: () => Promise<void>;
  loadMoreHolidays: () => Promise<void>;
  clearError: () => void;
}

export const useHolidays = (initialFilters: HolidayFilters = {}): UseHolidaysReturn => {
  const [state, setState] = useState<UseHolidaysState>({
    holidays: [],
    loading: false,
    error: null,
    pagination: undefined,
    refreshing: false,
  });

  const [currentFilters, setCurrentFilters] = useState<HolidayFilters>({
    country: APP_CONFIG.defaultCountry,
    limit: APP_CONFIG.itemsPerPage,
    ...initialFilters,
  });

  const updateState = useCallback((updates: Partial<UseHolidaysState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const clearError = useCallback(() => {
    updateState({ error: null });
  }, [updateState]);

  const fetchHolidays = useCallback(async (filters: HolidayFilters = {}) => {
    try {
      updateState({ loading: true, error: null });
      
      const mergedFilters = { ...currentFilters, ...filters };
      setCurrentFilters(mergedFilters);
      
      const response = await apiService.getHolidays(mergedFilters);
      
      if (response.success) {
        updateState({
          holidays: response.data,
          pagination: response.pagination,
          loading: false,
        });
      } else {
        throw new Error('Failed to fetch holidays');
      }
    } catch (error) {
      console.error('Error fetching holidays:', error);
      updateState({
        error: error instanceof Error ? error.message : 'Failed to fetch holidays',
        loading: false,
      });
    }
  }, [currentFilters, updateState]);

  const fetchUpcomingHolidays = useCallback(async (
    country: string = APP_CONFIG.defaultCountry,
    limit: number = APP_CONFIG.maxUpcomingHolidays
  ) => {
    try {
      updateState({ loading: true, error: null });

      const response = await apiService.getUpcomingHolidays(country, limit);

      if (response.success) {
        updateState({
          holidays: response.data,
          loading: false,
        });
      } else {
        throw new Error('Failed to fetch upcoming holidays');
      }
    } catch (error) {
      console.error('Error fetching upcoming holidays:', error);
      updateState({
        error: error instanceof Error ? error.message : 'Failed to fetch upcoming holidays',
        loading: false,
      });
    }
  }, [updateState]);

  const fetchUpcomingHolidaysForCountries = useCallback(async (
    countries: string[],
    limit: number = APP_CONFIG.maxUpcomingHolidays
  ) => {
    try {
      updateState({ loading: true, error: null });

      const response = await apiService.getUpcomingHolidaysForCountries(countries, limit);

      if (response.success) {
        updateState({
          holidays: response.data,
          loading: false,
        });
      } else {
        throw new Error('Failed to fetch upcoming holidays for countries');
      }
    } catch (error) {
      console.error('Error fetching upcoming holidays for countries:', error);
      updateState({
        error: error instanceof Error ? error.message : 'Failed to fetch upcoming holidays for countries',
        loading: false,
      });
    }
  }, [updateState]);

  const searchHolidays = useCallback(async (
    query: string,
    country: string = APP_CONFIG.defaultCountry
  ) => {
    try {
      updateState({ loading: true, error: null });
      
      const response = await apiService.searchHolidays(query, country);
      
      if (response.success) {
        updateState({
          holidays: response.data,
          loading: false,
        });
      } else {
        throw new Error('Failed to search holidays');
      }
    } catch (error) {
      console.error('Error searching holidays:', error);
      updateState({
        error: error instanceof Error ? error.message : 'Failed to search holidays',
        loading: false,
      });
    }
  }, [updateState]);

  const refreshHolidays = useCallback(async () => {
    try {
      updateState({ refreshing: true, error: null });
      
      const response = await apiService.getHolidays(currentFilters);
      
      if (response.success) {
        updateState({
          holidays: response.data,
          pagination: response.pagination,
          refreshing: false,
        });
      } else {
        throw new Error('Failed to refresh holidays');
      }
    } catch (error) {
      console.error('Error refreshing holidays:', error);
      updateState({
        error: error instanceof Error ? error.message : 'Failed to refresh holidays',
        refreshing: false,
      });
    }
  }, [currentFilters, updateState]);

  const loadMoreHolidays = useCallback(async () => {
    if (!state.pagination?.hasNext || state.loading) return;

    try {
      updateState({ loading: true, error: null });
      
      const nextPage = (state.pagination.current || 0) + 1;
      const response = await apiService.getHolidays({
        ...currentFilters,
        page: nextPage,
      });
      
      if (response.success) {
        updateState({
          holidays: [...state.holidays, ...response.data],
          pagination: response.pagination,
          loading: false,
        });
      } else {
        throw new Error('Failed to load more holidays');
      }
    } catch (error) {
      console.error('Error loading more holidays:', error);
      updateState({
        error: error instanceof Error ? error.message : 'Failed to load more holidays',
        loading: false,
      });
    }
  }, [state.holidays, state.pagination, state.loading, currentFilters, updateState]);

  // Initial fetch
  useEffect(() => {
    fetchHolidays();
  }, []);

  return {
    ...state,
    fetchHolidays,
    fetchUpcomingHolidays,
    fetchUpcomingHolidaysForCountries,
    searchHolidays,
    refreshHolidays,
    loadMoreHolidays,
    clearError,
  };
};
