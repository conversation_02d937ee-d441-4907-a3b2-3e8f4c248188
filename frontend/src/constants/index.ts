import { Dimensions } from 'react-native';

// API Configuration
export const API_BASE_URL = "https://holivia-backend.vercel.app/api";
// export const API_BASE_URL = __DEV__
//   ? 'http://172.20.10.5:5000/api'
//   : 'https://holivia-backend.vercel.app/api';

// Screen Dimensions
export const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Colors
export const COLORS = {
  primary: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },
  secondary: {
    50: '#f7fee7',
    100: '#ecfccb',
    200: '#d9f99d',
    300: '#bef264',
    400: '#a3e635',
    500: '#84cc16',
    600: '#65a30d',
    700: '#4d7c0f',
    800: '#3f6212',
    900: '#365314',
  },
  accent: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
  },
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },
  white: '#ffffff',
  black: '#000000',
  error: '#ef4444',
  warning: '#f59e0b',
  success: '#10b981',
};

// Holiday Types
export const HOLIDAY_TYPES = {
  national: {
    label: 'National',
    color: COLORS.primary[500],
    icon: 'flag',
  },
  religious: {
    label: 'Religious',
    color: COLORS.accent[500],
    icon: 'church',
  },
  cultural: {
    label: 'Cultural',
    color: COLORS.secondary[500],
    icon: 'people',
  },
  regional: {
    label: 'Regional',
    color: COLORS.gray[500],
    icon: 'location',
  },
};

// Countries
export const COUNTRIES = [
  { code: 'Nigeria', name: 'Nigeria', flag: '🇳🇬' },
  { code: 'United States', name: 'United States', flag: '🇺🇸' },
  { code: 'United Kingdom', name: 'United Kingdom', flag: '🇬🇧' },
  { code: 'Canada', name: 'Canada', flag: '🇨🇦' },
  { code: 'Australia', name: 'Australia', flag: '🇦🇺' },
];

// Months
export const MONTHS = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
];

// Days of the week
export const DAYS_OF_WEEK = [
  'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'
];

// App Configuration
export const APP_CONFIG = {
  name: 'Holivia',
  version: '1.0.0',
  description: 'Local Public Holiday Reminder',
  defaultCountry: 'Nigeria',
  itemsPerPage: 20,
  maxUpcomingHolidays: 5,
};

// Storage Keys
export const STORAGE_KEYS = {
  USER_PREFERENCES: '@holivia_user_preferences',
  SELECTED_COUNTRY: '@holivia_selected_country',
  NOTIFICATION_SETTINGS: '@holivia_notification_settings',
  CACHED_HOLIDAYS: '@holivia_cached_holidays',
  LAST_SYNC: '@holivia_last_sync',
  ONBOARDING_COMPLETED: '@holivia_onboarding_completed',
  REMINDER_SCHEDULE: '@holivia_reminder_schedule',
};

// Notification Settings
export const NOTIFICATION_TYPES = {
  SAME_DAY: 'same_day',
  ONE_DAY_BEFORE: 'one_day_before',
  THREE_DAYS_BEFORE: 'three_days_before',
  ONE_WEEK_BEFORE: 'one_week_before',
};

// Animation Durations
export const ANIMATION_DURATION = {
  SHORT: 200,
  MEDIUM: 300,
  LONG: 500,
};

// Spacing
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

// Border Radius
export const BORDER_RADIUS = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  full: 9999,
};

// Font Sizes
export const FONT_SIZES = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 32,
  xxxxl: 40,
};

// Cache Settings
export const CACHE_SETTINGS = {
  EXPIRY_HOURS: 24, // Cache expires after 24 hours
  MAX_CACHE_SIZE: 1000, // Maximum number of holidays to cache
  AUTO_REFRESH_INTERVAL: 6 * 60 * 60 * 1000, // 6 hours in milliseconds
} as const;

// Notification Settings
export const NOTIFICATION_SETTINGS = {
  CHANNEL_ID: 'holivia_reminders',
  CHANNEL_NAME: 'Holiday Reminders',
  CHANNEL_DESCRIPTION: 'Notifications for upcoming holidays',
  DEFAULT_SOUND: 'default', // Uses system default notification sound
  SOUND_DURATION: 3000, // 3 seconds
  VIBRATION_PATTERN: [0, 250, 250, 250], // Vibration pattern
  PRIORITY: 'high',
} as const;
