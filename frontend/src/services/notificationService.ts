import * as Notifications from 'expo-notifications';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import { Holiday } from '../types';
import { STORAGE_KEYS, NOTIFICATION_SETTINGS, API_BASE_URL } from '../constants';
import {
  getCurrentMonthEndInfo,
  getNextMonthEndInfo,
  shouldTriggerMonthEndReminder,
  getMonthEndReminderMessage,
  MonthEndInfo
} from '../utils';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowBanner: true,
    shouldShowList: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export interface NotificationPreferences {
  enabled: boolean;
  reminderDays: number[];
  sound: boolean;
  vibration: boolean;
}

export interface ScheduledNotification {
  id: string;
  holidayId: string;
  holidayName: string;
  scheduledDate: string;
  reminderDays: number;
}

export interface MonthEndNotification {
  id: string;
  monthEndDate: string;
  monthName: string;
  year: number;
  month: number;
  scheduledDate: string;
  reminderDays: number;
}

class NotificationService {
  private static instance: NotificationService;
  
  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  // Request notification permissions
  async requestPermissions(): Promise<boolean> {
    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.log('Notification permissions not granted');
        return false;
      }

      // Configure notification channel for Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync(NOTIFICATION_SETTINGS.CHANNEL_ID, {
          name: NOTIFICATION_SETTINGS.CHANNEL_NAME,
          description: NOTIFICATION_SETTINGS.CHANNEL_DESCRIPTION,
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: NOTIFICATION_SETTINGS.VIBRATION_PATTERN,
          sound: 'default',
          enableVibrate: true,
          enableLights: true,
          lightColor: '#22c55e',
        });
      }

      return true;
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  }

  // Get notification preferences
  async getPreferences(): Promise<NotificationPreferences> {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEYS.NOTIFICATION_SETTINGS);
      if (stored) {
        return JSON.parse(stored);
      }
      
      // Default preferences
      return {
        enabled: true,
        reminderDays: [1, 7],
        sound: true,
        vibration: true,
      };
    } catch (error) {
      console.error('Error getting notification preferences:', error);
      return {
        enabled: false,
        reminderDays: [],
        sound: true,
        vibration: true,
      };
    }
  }

  // Save notification preferences
  async savePreferences(preferences: NotificationPreferences): Promise<void> {
    try {
      await AsyncStorage.setItem(
        STORAGE_KEYS.NOTIFICATION_SETTINGS,
        JSON.stringify(preferences)
      );
    } catch (error) {
      console.error('Error saving notification preferences:', error);
    }
  }

  // Schedule notifications for a holiday
  async scheduleHolidayNotifications(holiday: Holiday): Promise<void> {
    try {
      const preferences = await this.getPreferences();
      
      if (!preferences.enabled || preferences.reminderDays.length === 0) {
        return;
      }

      const holidayDate = new Date(holiday.date);
      const now = new Date();

      for (const reminderDays of preferences.reminderDays) {
        const notificationDate = new Date(holidayDate);
        notificationDate.setDate(holidayDate.getDate() - reminderDays);
        notificationDate.setHours(9, 0, 0, 0); // 9 AM notification

        // Only schedule future notifications
        if (notificationDate > now) {
          const notificationId = `${holiday._id}_${reminderDays}`;
          
          await Notifications.scheduleNotificationAsync({
            identifier: notificationId,
            content: {
              title: reminderDays === 0 
                ? `🎉 ${holiday.name} is today!`
                : `🗓️ ${holiday.name} in ${reminderDays} day${reminderDays === 1 ? '' : 's'}`,
              body: holiday.description,
              data: {
                holidayId: holiday._id,
                holidayName: holiday.name,
                reminderDays,
                type: 'holiday_reminder',
              },
              sound: preferences.sound ? 'default' : false,
              badge: 1,
            },
            trigger: {
              date: notificationDate,
            },
          });

          // Save scheduled notification info
          await this.saveScheduledNotification({
            id: notificationId,
            holidayId: holiday._id,
            holidayName: holiday.name,
            scheduledDate: notificationDate.toISOString(),
            reminderDays,
          });
        }
      }
    } catch (error) {
      console.error('Error scheduling holiday notifications:', error);
    }
  }

  // Schedule notifications for multiple holidays
  async scheduleMultipleHolidayNotifications(holidays: Holiday[]): Promise<void> {
    try {
      // Clear existing notifications first
      await this.cancelAllNotifications();

      // Schedule new notifications
      for (const holiday of holidays) {
        await this.scheduleHolidayNotifications(holiday);
      }
    } catch (error) {
      console.error('Error scheduling multiple holiday notifications:', error);
    }
  }

  // Schedule notifications for holidays from multiple countries
  async scheduleNotificationsForCountries(countries: string[]): Promise<void> {
    try {
      const { apiService } = await import('./api');

      // Fetch upcoming holidays for all selected countries
      const response = await apiService.getUpcomingHolidaysForCountries(countries, 20);

      if (response.success && response.data) {
        await this.scheduleMultipleHolidayNotifications(response.data);
        console.log(`✅ Scheduled notifications for ${response.data.length} holidays from ${countries.length} countries`);
      }
    } catch (error) {
      console.error('Error scheduling notifications for multiple countries:', error);
    }
  }

  // Cancel all notifications
  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      await AsyncStorage.removeItem(STORAGE_KEYS.REMINDER_SCHEDULE);
    } catch (error) {
      console.error('Error canceling notifications:', error);
    }
  }

  // Cancel notifications for a specific holiday
  async cancelHolidayNotifications(holidayId: string): Promise<void> {
    try {
      const scheduled = await this.getScheduledNotifications();
      const toCancel = scheduled.filter(n => n.holidayId === holidayId);
      
      for (const notification of toCancel) {
        await Notifications.cancelScheduledNotificationAsync(notification.id);
      }
      
      // Update stored scheduled notifications
      const remaining = scheduled.filter(n => n.holidayId !== holidayId);
      await this.saveScheduledNotifications(remaining);
    } catch (error) {
      console.error('Error canceling holiday notifications:', error);
    }
  }

  // Get all scheduled notifications
  async getScheduledNotifications(): Promise<ScheduledNotification[]> {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEYS.REMINDER_SCHEDULE);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error getting scheduled notifications:', error);
      return [];
    }
  }

  // Save scheduled notification info
  private async saveScheduledNotification(notification: ScheduledNotification): Promise<void> {
    try {
      const existing = await this.getScheduledNotifications();
      existing.push(notification);
      await this.saveScheduledNotifications(existing);
    } catch (error) {
      console.error('Error saving scheduled notification:', error);
    }
  }

  // Save all scheduled notifications
  private async saveScheduledNotifications(notifications: ScheduledNotification[]): Promise<void> {
    try {
      await AsyncStorage.setItem(
        STORAGE_KEYS.REMINDER_SCHEDULE,
        JSON.stringify(notifications)
      );
    } catch (error) {
      console.error('Error saving scheduled notifications:', error);
    }
  }

  // Handle notification received
  handleNotificationReceived(notification: Notifications.Notification): void {
    const data = notification.request.content.data;
    console.log('Notification received:', {
      title: notification.request.content.title,
      body: notification.request.content.body,
      data: data
    });
    // You can add custom handling here
  }

  // Handle notification response (when user taps notification)
  handleNotificationResponse(response: Notifications.NotificationResponse): void {
    console.log('Notification response:', response);
    const data = response.notification.request.content.data;

    if (data?.type === 'holiday_reminder') {
      // Navigate to holiday detail or perform other actions
      console.log('Holiday reminder tapped:', data.holidayName);
    } else if (data?.type === 'month_end_reminder' || data?.type === 'month_end_alert') {
      // Handle month-end reminder tap
      console.log('Month-end reminder tapped:', data.monthName, data.year);
      // Could navigate to calendar view or show month-end info
    }
  }

  // Month-end notification methods
  async scheduleMonthEndNotifications(): Promise<void> {
    try {
      const preferences = await this.getPreferences();

      if (!preferences.enabled || preferences.reminderDays.length === 0) {
        return;
      }

      // Cancel existing month-end notifications first
      await this.cancelMonthEndNotifications();

      // Schedule for current month
      const currentMonthInfo = getCurrentMonthEndInfo();
      await this.scheduleMonthEndNotificationsForMonth(currentMonthInfo, preferences);

      // Schedule for next month
      const nextMonthInfo = getNextMonthEndInfo();
      await this.scheduleMonthEndNotificationsForMonth(nextMonthInfo, preferences);

    } catch (error) {
      console.error('Error scheduling month-end notifications:', error);
    }
  }

  private async scheduleMonthEndNotificationsForMonth(
    monthInfo: MonthEndInfo,
    preferences: NotificationPreferences
  ): Promise<void> {
    try {
      for (const reminderDays of preferences.reminderDays) {
        const reminderDate = new Date(monthInfo.lastDay);
        reminderDate.setDate(reminderDate.getDate() - reminderDays);

        // Only schedule if reminder date is in the future
        if (reminderDate > new Date()) {
          const notificationId = await Notifications.scheduleNotificationAsync({
            content: {
              title: '📅 Month End Reminder',
              body: getMonthEndReminderMessage({
                ...monthInfo,
                daysUntilEnd: reminderDays
              }),
              data: {
                type: 'month_end_reminder',
                monthName: monthInfo.monthName,
                year: monthInfo.year,
                month: monthInfo.month,
                reminderDays
              },
              sound: preferences.sound ? 'default' : false,
              badge: 1,
            },
            trigger: { date: reminderDate },
          });

          // Save notification info
          const monthEndNotification: MonthEndNotification = {
            id: notificationId,
            monthEndDate: monthInfo.lastDay.toISOString(),
            monthName: monthInfo.monthName,
            year: monthInfo.year,
            month: monthInfo.month,
            scheduledDate: reminderDate.toISOString(),
            reminderDays
          };

          await this.saveMonthEndNotification(monthEndNotification);
        }
      }
    } catch (error) {
      console.error('Error scheduling month-end notifications for month:', error);
    }
  }

  async cancelMonthEndNotifications(): Promise<void> {
    try {
      const scheduled = await this.getScheduledMonthEndNotifications();

      for (const notification of scheduled) {
        await Notifications.cancelScheduledNotificationAsync(notification.id);
      }

      // Clear stored month-end notifications
      await AsyncStorage.removeItem(STORAGE_KEYS.REMINDER_SCHEDULE + '_month_end');
    } catch (error) {
      console.error('Error canceling month-end notifications:', error);
    }
  }

  async getScheduledMonthEndNotifications(): Promise<MonthEndNotification[]> {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEYS.REMINDER_SCHEDULE + '_month_end');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error getting scheduled month-end notifications:', error);
      return [];
    }
  }

  private async saveMonthEndNotification(notification: MonthEndNotification): Promise<void> {
    try {
      const existing = await this.getScheduledMonthEndNotifications();
      existing.push(notification);
      await AsyncStorage.setItem(
        STORAGE_KEYS.REMINDER_SCHEDULE + '_month_end',
        JSON.stringify(existing)
      );
    } catch (error) {
      console.error('Error saving month-end notification:', error);
    }
  }

  async checkAndTriggerMonthEndReminder(): Promise<void> {
    try {
      const preferences = await this.getPreferences();

      if (!preferences.enabled || preferences.reminderDays.length === 0) {
        return;
      }

      if (shouldTriggerMonthEndReminder(preferences.reminderDays)) {
        const monthInfo = getCurrentMonthEndInfo();

        await Notifications.scheduleNotificationAsync({
          content: {
            title: '📅 Month End Alert',
            body: getMonthEndReminderMessage(monthInfo),
            data: {
              type: 'month_end_alert',
              monthName: monthInfo.monthName,
              year: monthInfo.year,
              month: monthInfo.month
            },
            sound: preferences.sound ? 'default' : false,
            badge: 1,
          },
          trigger: { seconds: 1 },
        });
      }
    } catch (error) {
      console.error('Error checking month-end reminder:', error);
    }
  }

  // Check for new country notifications from backend
  async checkForNewCountryNotifications(): Promise<void> {
    try {
      const deviceId = await this.getDeviceId();
      const baseUrl = API_BASE_URL.replace('/api', ''); // Remove /api suffix since we're adding it below
      const response = await fetch(`${baseUrl}/api/notifications/${deviceId}/latest?limit=5`);

      if (response.ok) {
        const data = await response.json();
        const notifications = data.data || [];

        // Filter for country_added notifications from the last hour
        const recentCountryNotifications = notifications.filter((notif: any) => {
          const isCountryAdded = notif.type === 'country_added';
          const isRecent = new Date(notif.createdAt).getTime() > Date.now() - (60 * 60 * 1000); // Last hour
          return isCountryAdded && isRecent;
        });

        // Show local notifications for new countries
        for (const notification of recentCountryNotifications) {
          await Notifications.scheduleNotificationAsync({
            content: {
              title: notification.title,
              body: notification.message,
              data: {
                type: 'country_notification',
                countries: notification.data?.countries || [],
                notificationId: notification._id
              },
              sound: true,
              badge: 1,
            },
            trigger: { seconds: 1 },
          });
        }
      }
    } catch (error) {
      console.error('Error checking for new country notifications:', error);
    }
  }

  private async getDeviceId(): Promise<string> {
    try {
      const deviceId = await AsyncStorage.getItem('device_id');
      if (deviceId) return deviceId;

      // Generate a simple device ID if none exists
      const newDeviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      await AsyncStorage.setItem('device_id', newDeviceId);
      return newDeviceId;
    } catch (error) {
      console.error('Error getting device ID:', error);
      return `device_${Date.now()}`;
    }
  }

  // Test notification (for debugging)
  async sendTestNotification(): Promise<void> {
    try {
      const preferences = await this.getPreferences();

      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🎉 Test Notification',
          body: 'This is a test notification from Holivia! If you can hear this sound, notifications are working properly.',
          data: { type: 'test' },
          sound: preferences.sound ? 'default' : false,
          badge: 1,
        },
        trigger: { seconds: 2 },
      });
    } catch (error) {
      console.error('Error sending test notification:', error);
    }
  }
}

export default NotificationService.getInstance();
