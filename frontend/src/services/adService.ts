import { Platform } from 'react-native';

// Conditional import for AdMob - only works in production builds, not Expo Go
let mobileAds: any = null;
let BannerAd: any = null;
let BannerAdSize: any = null;
let TestIds: any = null;
let InterstitialAd: any = null;
let AdEventType: any = null;
let RewardedAd: any = null;
let RewardedAdEventType: any = null;

try {
  const adMobModule = require('react-native-google-mobile-ads');
  mobileAds = adMobModule.default;
  BannerAd = adMobModule.BannerAd;
  BannerAdSize = adMobModule.BannerAdSize;
  TestIds = adMobModule.TestIds;
  InterstitialAd = adMobModule.InterstitialAd;
  AdEventType = adMobModule.AdEventType;
  RewardedAd = adMobModule.RewardedAd;
  RewardedAdEventType = adMobModule.RewardedAdEventType;
} catch (error) {
  // AdMob not available in Expo Go - this is expected
}

// Ad Unit IDs - Replace with your actual Ad Unit IDs from AdMob console
const AD_UNIT_IDS = {
  banner: __DEV__
    ? (TestIds?.BANNER || 'ca-app-pub-3940256099942544/6300978111') // Google test banner ID
    : Platform.OS === 'ios'
      ? 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX' // Replace with your iOS banner ad unit ID
      : 'ca-app-pub-1572104590091136/2967084238', // Replace with your Android banner ad unit ID

  interstitial: __DEV__
    ? (TestIds?.INTERSTITIAL || 'ca-app-pub-3940256099942544/1033173712') // Google test interstitial ID
    : Platform.OS === 'ios'
      ? 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX' // Replace with your iOS interstitial ad unit ID
      : 'ca-app-pub-1572104590091136/1626082404', // Replace with your Android interstitial ad unit ID

  rewarded: __DEV__
    ? (TestIds?.REWARDED || 'ca-app-pub-3940256099942544/5224354917') // Google test rewarded ID
    : Platform.OS === 'ios'
      ? 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX' // Replace with your iOS rewarded ad unit ID
      : 'ca-app-pub-1572104590091136/8640683949', // Replace with your Android rewarded ad unit ID
};

class AdService {
  private static instance: AdService;
  private interstitialAd: any = null;
  private rewardedAd: any = null;
  private isInitialized = false;
  private interstitialLoaded = false;
  private rewardedLoaded = false;
  private adViewCount = 0;
  private lastInterstitialTime = 0;
  private isAdMobAvailable = false;

  private constructor() {}

  static getInstance(): AdService {
    if (!AdService.instance) {
      AdService.instance = new AdService();
    }
    return AdService.instance;
  }

  async initialize(): Promise<void> {
    try {
      if (!mobileAds) {
        this.isAdMobAvailable = false;
        return;
      }

      await mobileAds().initialize();
      this.isInitialized = true;
      this.isAdMobAvailable = true;
      console.log('✅ AdMob initialized successfully');

      // Preload ads
      this.loadInterstitialAd();
      this.loadRewardedAd();
    } catch (error) {
      console.error('❌ Error initializing AdMob:', error);
      this.isAdMobAvailable = false;
    }
  }

  // Banner Ad Component (to be used in React components)
  getBannerAdUnitId(): string {
    return AD_UNIT_IDS.banner;
  }

  // Interstitial Ads
  private loadInterstitialAd(): void {
    if (!this.isInitialized || !this.isAdMobAvailable || !InterstitialAd || !AdEventType) return;

    this.interstitialAd = InterstitialAd.createForAdRequest(AD_UNIT_IDS.interstitial);

    this.interstitialAd.addAdEventListener(AdEventType.LOADED, () => {
      this.interstitialLoaded = true;
      console.log('📱 Interstitial ad loaded');
    });

    this.interstitialAd.addAdEventListener(AdEventType.ERROR, (error: any) => {
      console.error('❌ Interstitial ad error:', error);
      this.interstitialLoaded = false;
    });

    this.interstitialAd.addAdEventListener(AdEventType.CLOSED, () => {
      console.log('📱 Interstitial ad closed');
      this.interstitialLoaded = false;
      this.lastInterstitialTime = Date.now();
      // Preload next ad
      setTimeout(() => this.loadInterstitialAd(), 1000);
    });

    this.interstitialAd.load();
  }

  async showInterstitialAd(): Promise<boolean> {
    if (!this.isInitialized || !this.isAdMobAvailable || !this.interstitialLoaded || !this.interstitialAd) {
      console.log('📱 Interstitial ad not available');
      return false;
    }

    // Don't show ads too frequently (minimum 2 minutes between interstitials)
    const timeSinceLastAd = Date.now() - this.lastInterstitialTime;
    if (timeSinceLastAd < 120000) {
      return false;
    }

    try {
      await this.interstitialAd.show();
      return true;
    } catch (error) {
      console.error('❌ Error showing interstitial ad:', error);
      return false;
    }
  }

  // Rewarded Ads
  private loadRewardedAd(): void {
    if (!this.isInitialized || !this.isAdMobAvailable || !RewardedAd || !RewardedAdEventType) return;

    this.rewardedAd = RewardedAd.createForAdRequest(AD_UNIT_IDS.rewarded);

    this.rewardedAd.addAdEventListener(RewardedAdEventType.LOADED, () => {
      this.rewardedLoaded = true;
      console.log('🎁 Rewarded ad loaded');
    });

    this.rewardedAd.addAdEventListener(RewardedAdEventType.ERROR, (error: any) => {
      console.error('❌ Rewarded ad error:', error);
      this.rewardedLoaded = false;
    });

    this.rewardedAd.addAdEventListener(RewardedAdEventType.EARNED_REWARD, (reward: any) => {
      console.log('🎁 User earned reward:', reward);
    });

    this.rewardedAd.addAdEventListener(AdEventType.CLOSED, () => {
      console.log('🎁 Rewarded ad closed');
      this.rewardedLoaded = false;
      // Preload next ad
      setTimeout(() => this.loadRewardedAd(), 1000);
    });

    this.rewardedAd.load();
  }

  async showRewardedAd(): Promise<{ success: boolean; reward?: any }> {
    if (!this.isInitialized || !this.isAdMobAvailable || !this.rewardedLoaded || !this.rewardedAd) {
      console.log('📱 Rewarded ad not available');
      return { success: false };
    }

    try {
      const reward = await this.rewardedAd.show();
      return { success: true, reward };
    } catch (error) {
      console.error('❌ Error showing rewarded ad:', error);
      return { success: false };
    }
  }

  // Smart ad showing logic
  shouldShowInterstitialAd(): boolean {
    this.adViewCount++;
    
    // Show interstitial every 4 screen views
    if (this.adViewCount % 4 === 0) {
      return true;
    }
    
    return false;
  }

  // Check if rewarded ad is available
  isRewardedAdReady(): boolean {
    return this.rewardedLoaded;
  }

  // Check if interstitial ad is ready
  isInterstitialAdReady(): boolean {
    return this.interstitialLoaded;
  }

  // Reset ad view count (useful for session management)
  resetAdViewCount(): void {
    this.adViewCount = 0;
  }
}

export default AdService.getInstance();
