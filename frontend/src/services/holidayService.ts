import { Holiday, HolidayResponse } from '../types/Holiday';

const API_BASE_URL = 'http://172.20.10.5:5000/api';

class HolidayService {
  async getUpcomingHolidays(limit: number = 10): Promise<Holiday[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/holidays/upcoming?limit=${limit}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data: HolidayResponse = await response.json();
      return data.holidays || [];
    } catch (error) {
      console.error('Error fetching upcoming holidays:', error);
      // Return mock data for development
      return this.getMockHolidays();
    }
  }

  async getAllHolidays(page: number = 1, limit: number = 20): Promise<HolidayResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/holidays?page=${page}&limit=${limit}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error fetching all holidays:', error);
      // Return mock data for development
      const mockHolidays = this.getMockHolidays();
      return {
        holidays: mockHolidays,
        total: mockHolidays.length,
        page: 1,
        limit: 20,
      };
    }
  }

  async searchHolidays(query: string): Promise<Holiday[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/holidays/search?q=${encodeURIComponent(query)}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data: HolidayResponse = await response.json();
      return data.holidays || [];
    } catch (error) {
      console.error('Error searching holidays:', error);
      // Return filtered mock data for development
      const mockHolidays = this.getMockHolidays();
      return mockHolidays.filter(holiday => 
        holiday.name.toLowerCase().includes(query.toLowerCase()) ||
        holiday.description?.toLowerCase().includes(query.toLowerCase())
      );
    }
  }

  async getHolidaysByMonth(year: number, month: number): Promise<Holiday[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/holidays/month/${year}/${month}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data: HolidayResponse = await response.json();
      return data.holidays || [];
    } catch (error) {
      console.error('Error fetching holidays by month:', error);
      // Return filtered mock data for development
      const mockHolidays = this.getMockHolidays();
      return mockHolidays.filter(holiday => {
        const holidayDate = new Date(holiday.date);
        return holidayDate.getFullYear() === year && holidayDate.getMonth() === month - 1;
      });
    }
  }

  private getMockHolidays(): Holiday[] {
    const currentYear = new Date().getFullYear();
    return [
      {
        id: '1',
        name: 'New Year\'s Day',
        date: `${currentYear + 1}-01-01`,
        description: 'The first day of the year in the Gregorian calendar',
        type: 'public',
        isPublic: true,
        country: 'Nigeria',
      },
      {
        id: '2',
        name: 'Good Friday',
        date: `${currentYear + 1}-03-29`,
        description: 'Christian holiday commemorating the crucifixion of Jesus Christ',
        type: 'religious',
        isPublic: true,
        country: 'Nigeria',
      },
      {
        id: '3',
        name: 'Easter Monday',
        date: `${currentYear + 1}-04-01`,
        description: 'Christian holiday celebrating the resurrection of Jesus Christ',
        type: 'religious',
        isPublic: true,
        country: 'Nigeria',
      },
      {
        id: '4',
        name: 'Workers\' Day',
        date: `${currentYear + 1}-05-01`,
        description: 'International Workers\' Day, also known as Labour Day',
        type: 'public',
        isPublic: true,
        country: 'Nigeria',
      },
      {
        id: '5',
        name: 'Children\'s Day',
        date: `${currentYear + 1}-05-27`,
        description: 'A day to celebrate children and promote their welfare',
        type: 'public',
        isPublic: true,
        country: 'Nigeria',
      },
      {
        id: '6',
        name: 'Democracy Day',
        date: `${currentYear + 1}-06-12`,
        description: 'Commemorates Nigeria\'s return to democratic rule',
        type: 'public',
        isPublic: true,
        country: 'Nigeria',
      },
      {
        id: '7',
        name: 'Independence Day',
        date: `${currentYear + 1}-10-01`,
        description: 'Celebrates Nigeria\'s independence from British colonial rule',
        type: 'national',
        isPublic: true,
        country: 'Nigeria',
      },
      {
        id: '8',
        name: 'Christmas Day',
        date: `${currentYear + 1}-12-25`,
        description: 'Christian holiday celebrating the birth of Jesus Christ',
        type: 'religious',
        isPublic: true,
        country: 'Nigeria',
      },
      {
        id: '9',
        name: 'Boxing Day',
        date: `${currentYear + 1}-12-26`,
        description: 'Traditional holiday following Christmas Day',
        type: 'public',
        isPublic: true,
        country: 'Nigeria',
      },
    ];
  }
}

export const holidayService = new HolidayService();
