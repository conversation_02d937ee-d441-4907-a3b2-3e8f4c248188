import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { API_BASE_URL } from '../constants';
import { Holiday, HolidayResponse, HolidayFilters, ApiResponse } from '../types';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('❌ API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log(`✅ API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('❌ API Response Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  // Health check
  async healthCheck(): Promise<ApiResponse> {
    try {
      const response = await this.api.get('/health');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get all holidays with filters
  async getHolidays(filters: HolidayFilters = {}): Promise<HolidayResponse> {
    try {
      const params = new URLSearchParams();

      // Ensure we don't fetch past years by default
      const currentYear = new Date().getFullYear();
      if (!filters.year) {
        filters.year = currentYear;
      }

      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });

      const response = await this.api.get(`/holidays?${params.toString()}`);

      // Additional client-side filtering for extra safety
      if (response.data.success && response.data.data) {
        const filteredHolidays = response.data.data.filter((holiday: Holiday) => {
          const holidayDate = new Date(holiday.date);
          const holidayYear = holidayDate.getFullYear();
          return holidayYear >= currentYear;
        });

        response.data.data = filteredHolidays;
      }

      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get upcoming holidays
  async getUpcomingHolidays(country: string = 'Nigeria', limit: number = 5): Promise<HolidayResponse> {
    try {
      const response = await this.api.get('/holidays/upcoming', {
        params: { country, limit }
      });

      // Additional client-side filtering to ensure no past holidays
      if (response.data.success && response.data.data) {
        const currentYear = new Date().getFullYear();
        const today = new Date();

        const filteredHolidays = response.data.data.filter((holiday: Holiday) => {
          const holidayDate = new Date(holiday.date);
          const holidayYear = holidayDate.getFullYear();
          return holidayYear >= currentYear && holidayDate >= today;
        });

        response.data.data = filteredHolidays;
      }

      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get upcoming holidays for multiple countries
  async getUpcomingHolidaysForCountries(countries: string[], limit: number = 5): Promise<HolidayResponse> {
    try {
      const allHolidays: Holiday[] = [];

      // Fetch holidays for each country
      for (const country of countries) {
        try {
          const response = await this.getUpcomingHolidays(country, limit);
          if (response.success && response.data) {
            allHolidays.push(...response.data);
          }
        } catch (error) {
          console.warn(`Failed to fetch holidays for ${country}:`, error);
          // Continue with other countries even if one fails
        }
      }

      // Filter out past holidays and sort by date
      const currentYear = new Date().getFullYear();
      const today = new Date();

      const sortedHolidays = allHolidays
        .filter(holiday => {
          const holidayDate = new Date(holiday.date);
          const holidayYear = holidayDate.getFullYear();
          // Only include holidays from current year onwards and not in the past
          return holidayYear >= currentYear && holidayDate >= today;
        })
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
        .slice(0, limit);

      return {
        success: true,
        data: sortedHolidays,
        meta: {
          count: sortedHolidays.length,
          country: countries.join(', ')
        }
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get holiday by ID
  async getHolidayById(id: string): Promise<{ success: boolean; data: Holiday }> {
    try {
      const response = await this.api.get(`/holidays/${id}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get holidays by month
  async getHolidaysByMonth(
    year: number, 
    month: number, 
    country: string = 'Nigeria'
  ): Promise<HolidayResponse> {
    try {
      const response = await this.api.get(`/holidays/by-month/${year}/${month}/${country}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Search holidays
  async searchHolidays(
    query: string,
    country: string = 'Nigeria',
    limit: number = 10
  ): Promise<HolidayResponse> {
    try {
      const currentYear = new Date().getFullYear();
      const response = await this.api.get('/holidays/search', {
        params: { q: query, country, limit, year: currentYear }
      });

      // Additional client-side filtering to ensure no past holidays
      if (response.data.success && response.data.data) {
        const filteredHolidays = response.data.data.filter((holiday: Holiday) => {
          const holidayDate = new Date(holiday.date);
          const holidayYear = holidayDate.getFullYear();
          return holidayYear >= currentYear;
        });

        response.data.data = filteredHolidays;
      }

      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Search holidays for multiple countries
  async searchHolidaysForCountries(
    query: string,
    countries: string[],
    limit: number = 10
  ): Promise<HolidayResponse> {
    try {
      const allHolidays: Holiday[] = [];

      // Search holidays for each country
      for (const country of countries) {
        try {
          const response = await this.searchHolidays(query, country, limit);
          if (response.success && response.data) {
            allHolidays.push(...response.data);
          }
        } catch (error) {
          console.warn(`Failed to search holidays for ${country}:`, error);
          // Continue with other countries even if one fails
        }
      }

      // Remove duplicates and sort by date
      const uniqueHolidays = allHolidays.filter((holiday, index, self) =>
        index === self.findIndex(h => h._id === holiday._id)
      );

      const sortedHolidays = uniqueHolidays
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
        .slice(0, limit);

      return {
        success: true,
        data: sortedHolidays,
        meta: {
          count: sortedHolidays.length,
          country: countries.join(', ')
        }
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get holidays for multiple countries
  async getHolidaysForCountries(
    countries: string[],
    filters: HolidayFilters = {}
  ): Promise<HolidayResponse> {
    try {
      const allHolidays: Holiday[] = [];

      // Fetch holidays for each country
      for (const country of countries) {
        try {
          const countryFilters = { ...filters, country };
          const response = await this.getHolidays(countryFilters);
          if (response.success && response.data) {
            allHolidays.push(...response.data);
          }
        } catch (error) {
          console.warn(`Failed to fetch holidays for ${country}:`, error);
          // Continue with other countries even if one fails
        }
      }

      // Remove duplicates and sort by date
      const uniqueHolidays = allHolidays.filter((holiday, index, self) =>
        index === self.findIndex(h => h._id === holiday._id)
      );

      const sortedHolidays = uniqueHolidays
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

      return {
        success: true,
        data: sortedHolidays,
        meta: {
          count: sortedHolidays.length,
          country: countries.join(', ')
        }
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get available countries
  async getCountries(): Promise<{ success: boolean; data: string[] }> {
    try {
      const response = await this.api.get('/holidays/countries');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Admin endpoints (for future use)
  async createHoliday(holidayData: Partial<Holiday>): Promise<ApiResponse<Holiday>> {
    try {
      const response = await this.api.post('/admin/holidays', holidayData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async updateHoliday(id: string, holidayData: Partial<Holiday>): Promise<ApiResponse<Holiday>> {
    try {
      const response = await this.api.put(`/admin/holidays/${id}`, holidayData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async deleteHoliday(id: string): Promise<ApiResponse> {
    try {
      const response = await this.api.delete(`/admin/holidays/${id}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getAdminStats(): Promise<ApiResponse> {
    try {
      const response = await this.api.get('/admin/stats');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Error handler
  private handleError(error: any): Error {
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.message || error.response.statusText;
      return new Error(`API Error: ${message}`);
    } else if (error.request) {
      // Request was made but no response received
      return new Error('Network Error: Unable to connect to server');
    } else {
      // Something else happened
      return new Error(`Request Error: ${error.message}`);
    }
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
