import AsyncStorage from '@react-native-async-storage/async-storage';
import { Holiday, HolidayResponse } from '../types';
import { STORAGE_KEYS, CACHE_SETTINGS } from '../constants';

export interface CacheEntry {
  data: Holiday[];
  timestamp: number;
  key: string;
  metadata?: {
    country?: string;
    year?: number;
    type?: string;
    query?: string;
  };
}

export interface CacheStats {
  totalEntries: number;
  totalSize: number;
  oldestEntry: number;
  newestEntry: number;
  lastCleanup: number;
}

class CacheService {
  private static instance: CacheService;
  
  public static getInstance(): CacheService {
    if (!CacheService.instance) {
      CacheService.instance = new CacheService();
    }
    return CacheService.instance;
  }

  // Generate cache key
  private generateCacheKey(params: {
    country?: string;
    year?: number;
    type?: string;
    query?: string;
    endpoint?: string;
  }): string {
    const { country, year, type, query, endpoint = 'holidays' } = params;
    const parts = [endpoint];
    
    if (country) parts.push(`country:${country}`);
    if (year) parts.push(`year:${year}`);
    if (type) parts.push(`type:${type}`);
    if (query) parts.push(`query:${query}`);
    
    return parts.join('_');
  }

  // Check if cache entry is valid
  private isCacheValid(timestamp: number): boolean {
    const now = Date.now();
    const expiryTime = CACHE_SETTINGS.EXPIRY_HOURS * 60 * 60 * 1000;
    return (now - timestamp) < expiryTime;
  }

  // Get cached data
  async get(params: {
    country?: string;
    year?: number;
    type?: string;
    query?: string;
    endpoint?: string;
  }): Promise<Holiday[] | null> {
    try {
      const cacheKey = this.generateCacheKey(params);
      const cached = await AsyncStorage.getItem(`${STORAGE_KEYS.CACHED_HOLIDAYS}_${cacheKey}`);
      
      if (!cached) {
        return null;
      }

      const cacheEntry: CacheEntry = JSON.parse(cached);
      
      // Check if cache is still valid
      if (!this.isCacheValid(cacheEntry.timestamp)) {
        await this.remove(cacheKey);
        return null;
      }

      console.log(`Cache hit for key: ${cacheKey}`);
      return cacheEntry.data;
    } catch (error) {
      console.error('Error getting cached data:', error);
      return null;
    }
  }

  // Set cached data
  async set(
    params: {
      country?: string;
      year?: number;
      type?: string;
      query?: string;
      endpoint?: string;
    },
    data: Holiday[]
  ): Promise<void> {
    try {
      const cacheKey = this.generateCacheKey(params);
      const cacheEntry: CacheEntry = {
        data,
        timestamp: Date.now(),
        key: cacheKey,
        metadata: {
          country: params.country,
          year: params.year,
          type: params.type,
          query: params.query,
        },
      };

      await AsyncStorage.setItem(
        `${STORAGE_KEYS.CACHED_HOLIDAYS}_${cacheKey}`,
        JSON.stringify(cacheEntry)
      );

      console.log(`Cache set for key: ${cacheKey}, items: ${data.length}`);
      
      // Update last sync time
      await AsyncStorage.setItem(STORAGE_KEYS.LAST_SYNC, Date.now().toString());
      
      // Cleanup old cache entries if needed
      await this.cleanupIfNeeded();
    } catch (error) {
      console.error('Error setting cached data:', error);
    }
  }

  // Remove specific cache entry
  async remove(cacheKey: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(`${STORAGE_KEYS.CACHED_HOLIDAYS}_${cacheKey}`);
      console.log(`Cache removed for key: ${cacheKey}`);
    } catch (error) {
      console.error('Error removing cached data:', error);
    }
  }

  // Clear all cache
  async clearAll(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(STORAGE_KEYS.CACHED_HOLIDAYS));
      
      await AsyncStorage.multiRemove(cacheKeys);
      await AsyncStorage.removeItem(STORAGE_KEYS.LAST_SYNC);
      
      console.log(`Cleared ${cacheKeys.length} cache entries`);
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  // Get cache statistics
  async getStats(): Promise<CacheStats> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(STORAGE_KEYS.CACHED_HOLIDAYS));
      
      let totalSize = 0;
      let oldestEntry = Date.now();
      let newestEntry = 0;
      
      for (const key of cacheKeys) {
        const cached = await AsyncStorage.getItem(key);
        if (cached) {
          const cacheEntry: CacheEntry = JSON.parse(cached);
          totalSize += cacheEntry.data.length;
          
          if (cacheEntry.timestamp < oldestEntry) {
            oldestEntry = cacheEntry.timestamp;
          }
          if (cacheEntry.timestamp > newestEntry) {
            newestEntry = cacheEntry.timestamp;
          }
        }
      }

      const lastCleanupStr = await AsyncStorage.getItem(`${STORAGE_KEYS.CACHED_HOLIDAYS}_last_cleanup`);
      const lastCleanup = lastCleanupStr ? parseInt(lastCleanupStr) : 0;

      return {
        totalEntries: cacheKeys.length,
        totalSize,
        oldestEntry: cacheKeys.length > 0 ? oldestEntry : 0,
        newestEntry: cacheKeys.length > 0 ? newestEntry : 0,
        lastCleanup,
      };
    } catch (error) {
      console.error('Error getting cache stats:', error);
      return {
        totalEntries: 0,
        totalSize: 0,
        oldestEntry: 0,
        newestEntry: 0,
        lastCleanup: 0,
      };
    }
  }

  // Cleanup old cache entries
  async cleanupIfNeeded(): Promise<void> {
    try {
      const stats = await this.getStats();
      
      // Check if cleanup is needed
      const shouldCleanup = 
        stats.totalSize > CACHE_SETTINGS.MAX_CACHE_SIZE ||
        (Date.now() - stats.lastCleanup) > (24 * 60 * 60 * 1000); // 24 hours
      
      if (!shouldCleanup) {
        return;
      }

      console.log('Starting cache cleanup...');
      
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(STORAGE_KEYS.CACHED_HOLIDAYS));
      
      // Get all cache entries with timestamps
      const entries: { key: string; timestamp: number; size: number }[] = [];
      
      for (const key of cacheKeys) {
        const cached = await AsyncStorage.getItem(key);
        if (cached) {
          const cacheEntry: CacheEntry = JSON.parse(cached);
          
          // Remove expired entries
          if (!this.isCacheValid(cacheEntry.timestamp)) {
            await AsyncStorage.removeItem(key);
            continue;
          }
          
          entries.push({
            key,
            timestamp: cacheEntry.timestamp,
            size: cacheEntry.data.length,
          });
        }
      }

      // If still too many entries, remove oldest ones
      if (entries.length > CACHE_SETTINGS.MAX_CACHE_SIZE / 10) {
        entries.sort((a, b) => a.timestamp - b.timestamp);
        const toRemove = entries.slice(0, Math.floor(entries.length / 3));
        
        for (const entry of toRemove) {
          await AsyncStorage.removeItem(entry.key);
        }
        
        console.log(`Removed ${toRemove.length} old cache entries`);
      }

      // Update last cleanup time
      await AsyncStorage.setItem(
        `${STORAGE_KEYS.CACHED_HOLIDAYS}_last_cleanup`,
        Date.now().toString()
      );
      
      console.log('Cache cleanup completed');
    } catch (error) {
      console.error('Error during cache cleanup:', error);
    }
  }

  // Get last sync time
  async getLastSyncTime(): Promise<number> {
    try {
      const lastSync = await AsyncStorage.getItem(STORAGE_KEYS.LAST_SYNC);
      return lastSync ? parseInt(lastSync) : 0;
    } catch (error) {
      console.error('Error getting last sync time:', error);
      return 0;
    }
  }

  // Check if auto refresh is needed
  async shouldAutoRefresh(): Promise<boolean> {
    try {
      const lastSync = await this.getLastSyncTime();
      const now = Date.now();
      
      return (now - lastSync) > CACHE_SETTINGS.AUTO_REFRESH_INTERVAL;
    } catch (error) {
      console.error('Error checking auto refresh:', error);
      return true;
    }
  }

  // Get cache size in MB
  async getCacheSizeInMB(): Promise<number> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(STORAGE_KEYS.CACHED_HOLIDAYS));
      
      let totalBytes = 0;
      
      for (const key of cacheKeys) {
        const cached = await AsyncStorage.getItem(key);
        if (cached) {
          totalBytes += new Blob([cached]).size;
        }
      }
      
      return totalBytes / (1024 * 1024); // Convert to MB
    } catch (error) {
      console.error('Error calculating cache size:', error);
      return 0;
    }
  }
}

export default CacheService.getInstance();
