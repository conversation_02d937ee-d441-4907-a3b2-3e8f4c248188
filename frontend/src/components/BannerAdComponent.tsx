import React, { useState } from 'react';
import { View, StyleSheet, Text } from 'react-native';
import adService from '../services/adService';
import { COLORS, SPACING } from '../constants';

// Check if AdMob is available
let isAdMobAvailable = false;
let BannerAd: any = null;
let BannerAdSize: any = null;

try {
  const adMobModule = require('react-native-google-mobile-ads');
  BannerAd = adMobModule.BannerAd;
  BannerAdSize = adMobModule.BannerAdSize;
  isAdMobAvailable = true;
} catch (error) {
  isAdMobAvailable = false;
}

interface BannerAdComponentProps {
  size?: any;
  style?: any;
  showOnlyInProduction?: boolean;
}

const BannerAdComponent: React.FC<BannerAdComponentProps> = ({
  size,
  style,
  showOnlyInProduction = false
}) => {
  // Don't show ads in development if showOnlyInProduction is true
  if (showOnlyInProduction && __DEV__) {
    return null;
  }

  // Show placeholder if AdMob is not available (Expo Go)
  if (!isAdMobAvailable) {
    if (__DEV__) {
      return (
        <View style={[styles.container, styles.placeholderContainer, style]}>
          <Text style={styles.placeholderText}>📱 Ad Placeholder (Expo Go)</Text>
        </View>
      );
    }
    return null;
  }

  // AdMob is available, render the actual ad component
  return <ActualBannerAd size={size} style={style} />;
};

// Separate component for actual AdMob banner ad
const ActualBannerAd: React.FC<{ size?: any; style?: any }> = ({ size, style }) => {
  const [adLoaded, setAdLoaded] = useState(false);
  const [adError, setAdError] = useState(false);

  const handleAdLoaded = () => {
    setAdLoaded(true);
    setAdError(false);
    console.log('📱 Banner ad loaded successfully');
  };

  const handleAdError = (error: any) => {
    setAdError(true);
    setAdLoaded(false);
    console.error('❌ Banner ad error:', error);
  };

  const handleAdOpened = () => {
    console.log('📱 Banner ad opened');
  };

  const handleAdClosed = () => {
    console.log('📱 Banner ad closed');
  };

  // Set default size
  const adSize = size || BannerAdSize?.BANNER || 'BANNER';

  return (
    <View style={[styles.container, style]}>
      <BannerAd
        unitId={adService.getBannerAdUnitId()}
        size={adSize}
        requestOptions={{
          requestNonPersonalizedAdsOnly: false,
        }}
        onAdLoaded={handleAdLoaded}
        onAdFailedToLoad={handleAdError}
        onAdOpened={handleAdOpened}
        onAdClosed={handleAdClosed}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.gray[50],
    borderRadius: 8,
    marginVertical: SPACING.sm,
    paddingVertical: SPACING.xs,
  },
  placeholderContainer: {
    height: 50,
    borderWidth: 1,
    borderColor: COLORS.gray[200],
    borderStyle: 'dashed',
  },
  placeholderText: {
    color: COLORS.gray[500],
    fontSize: 12,
    fontWeight: '500',
  },
});

export default BannerAdComponent;
