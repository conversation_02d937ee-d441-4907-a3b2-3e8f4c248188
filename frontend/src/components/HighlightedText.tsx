import React from 'react';
import { Text, TextStyle } from 'react-native';
import { COLORS } from '../constants';

interface HighlightedTextProps {
  text: string;
  searchQuery: string;
  style?: TextStyle;
  highlightStyle?: TextStyle;
}

const HighlightedText: React.FC<HighlightedTextProps> = ({
  text,
  searchQuery,
  style,
  highlightStyle
}) => {
  if (!searchQuery || !text) {
    return <Text style={style}>{text}</Text>;
  }

  // Create regex for case-insensitive search
  const regex = new RegExp(`(${searchQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  const parts = text.split(regex);

  return (
    <Text style={style}>
      {parts.map((part, index) => {
        const isHighlight = regex.test(part);
        return (
          <Text
            key={index}
            style={isHighlight ? [defaultHighlightStyle, highlightStyle] : undefined}
          >
            {part}
          </Text>
        );
      })}
    </Text>
  );
};

const defaultHighlightStyle: TextStyle = {
  backgroundColor: COLORS.primary[100],
  color: COLORS.primary[800],
  fontWeight: '600',
};

export default HighlightedText;
