import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONT_SIZES } from '../constants';

interface AppLogoProps {
  size?: 'small' | 'medium' | 'large';
  showText?: boolean;
  style?: any;
}

const AppLogo: React.FC<AppLogoProps> = ({ 
  size = 'medium', 
  showText = true, 
  style 
}) => {
  const getSizes = () => {
    switch (size) {
      case 'small':
        return {
          container: 40,
          icon: 20,
          text: FONT_SIZES.md,
        };
      case 'large':
        return {
          container: 120,
          icon: 60,
          text: FONT_SIZES.xxl,
        };
      default: // medium
        return {
          container: 80,
          icon: 40,
          text: FONT_SIZES.xl,
        };
    }
  };

  const sizes = getSizes();

  return (
    <View style={[styles.container, style]}>
      <View
        style={[
          styles.logoContainer,
          {
            width: sizes.container,
            height: sizes.container,
            borderRadius: sizes.container / 2,
          },
        ]}
      >
        <Image
          source={require('../../assets/logo.png')}
          style={[
            styles.logoImage,
            {
              width: sizes.container,
              height: sizes.container,
              borderRadius: sizes.container / 2,
            },
          ]}
          resizeMode="cover"
        />
      </View>

      {showText && (
        <Text style={[styles.logoText, { fontSize: sizes.text }]}>
          Holivia
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  logoContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    position: 'relative',
    backgroundColor: COLORS.white, // Background for the logo
  },
  logoImage: {
    // Image will inherit size from parent container
  },
  logoText: {
    fontWeight: 'bold',
    color: COLORS.primary[600],
    marginTop: 8,
    letterSpacing: 1,
  },
});

export default AppLogo;
