import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONT_SIZES } from '../constants';

interface AppLogoProps {
  size?: 'small' | 'medium' | 'large';
  showText?: boolean;
  style?: any;
}

const AppLogo: React.FC<AppLogoProps> = ({ 
  size = 'medium', 
  showText = true, 
  style 
}) => {
  const getSizes = () => {
    switch (size) {
      case 'small':
        return {
          container: 40,
          icon: 20,
          text: FONT_SIZES.md,
        };
      case 'large':
        return {
          container: 120,
          icon: 60,
          text: FONT_SIZES.xxl,
        };
      default: // medium
        return {
          container: 80,
          icon: 40,
          text: FONT_SIZES.xl,
        };
    }
  };

  const sizes = getSizes();

  return (
    <View style={[styles.container, style]}>
      <LinearGradient
        colors={[COLORS.primary[500], COLORS.primary[600], COLORS.primary[700]]}
        style={[
          styles.logoContainer,
          {
            width: sizes.container,
            height: sizes.container,
            borderRadius: sizes.container / 2,
          },
        ]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <Ionicons 
          name="calendar" 
          size={sizes.icon} 
          color={COLORS.white} 
        />
        
        {/* Calendar details overlay */}
        <View style={styles.calendarDetails}>
          <View style={[styles.calendarLine, { width: sizes.icon * 0.6 }]} />
          <View style={[styles.calendarLine, { width: sizes.icon * 0.4 }]} />
          <View style={[styles.calendarDot, { 
            width: sizes.icon * 0.1, 
            height: sizes.icon * 0.1,
            borderRadius: sizes.icon * 0.05,
          }]} />
        </View>
      </LinearGradient>
      
      {showText && (
        <Text style={[styles.logoText, { fontSize: sizes.text }]}>
          Holivia
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  logoContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    position: 'relative',
  },
  calendarDetails: {
    position: 'absolute',
    bottom: '25%',
    alignItems: 'center',
  },
  calendarLine: {
    height: 1,
    backgroundColor: 'rgba(255,255,255,0.6)',
    marginBottom: 2,
  },
  calendarDot: {
    backgroundColor: COLORS.accent[400],
    marginTop: 2,
  },
  logoText: {
    fontWeight: 'bold',
    color: COLORS.primary[600],
    marginTop: 8,
    letterSpacing: 1,
  },
});

export default AppLogo;
