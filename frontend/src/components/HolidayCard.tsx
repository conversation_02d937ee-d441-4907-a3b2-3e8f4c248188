import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Holiday } from '../types';
import { COLORS, SPACING, BORDER_RADIUS, FONT_SIZES } from '../constants';
import {
  formatDateLong,
  getRelativeDateDescription,
  getHolidayTypeInfo,
  truncateText
} from '../utils';
import HighlightedText from './HighlightedText';

interface HolidayCardProps {
  holiday: Holiday;
  onPress?: () => void;
  compact?: boolean;
  searchQuery?: string; // For highlighting search terms
}

const HolidayCard: React.FC<HolidayCardProps> = ({
  holiday,
  onPress,
  compact = false,
  searchQuery
}) => {
  const typeInfo = getHolidayTypeInfo(holiday.type);
  const relativeDate = getRelativeDateDescription(holiday.date);
  
  return (
    <TouchableOpacity 
      style={styles.container} 
      onPress={onPress}
      activeOpacity={0.7}
    >
      <LinearGradient
        colors={[COLORS.white, COLORS.gray[50]]}
        style={styles.gradient}
      >
        <View style={styles.content}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.titleContainer}>
              <HighlightedText
                text={holiday.name}
                searchQuery={searchQuery || ''}
                style={styles.title}
              />
              <View style={[styles.typeBadge, { backgroundColor: typeInfo.color }]}>
                <Text style={styles.typeText}>{typeInfo.label}</Text>
              </View>
            </View>
            <Ionicons 
              name="chevron-forward" 
              size={20} 
              color={COLORS.gray[400]} 
            />
          </View>

          {/* Date Information */}
          <View style={styles.dateContainer}>
            <View style={styles.dateInfo}>
              <Ionicons
                name="calendar-outline"
                size={16}
                color={COLORS.gray[500]}
              />
              <Text style={styles.dateText}>
                {formatDateLong(holiday.date)}
              </Text>
            </View>
            <Text style={[styles.relativeDate, { color: typeInfo.color }]}>
              {relativeDate}
            </Text>
          </View>

          {/* Country Information */}
          <View style={styles.countryContainer}>
            <Ionicons
              name="location-outline"
              size={14}
              color={COLORS.gray[500]}
            />
            <Text style={styles.countryText}>
              {holiday.country}
            </Text>
          </View>

          {/* Description */}
          {!compact && (
            <HighlightedText
              text={truncateText(holiday.description, 120)}
              searchQuery={searchQuery || ''}
              style={styles.description}
            />
          )}

          {/* Activities Preview */}
          {!compact && holiday.activities && holiday.activities.length > 0 && (
            <View style={styles.activitiesContainer}>
              <Ionicons 
                name="list-outline" 
                size={14} 
                color={COLORS.gray[500]} 
              />
              <Text style={styles.activitiesText} numberOfLines={1}>
                {holiday.activities.slice(0, 2).join(', ')}
                {holiday.activities.length > 2 && '...'}
              </Text>
            </View>
          )}
        </View>

        {/* Accent Border */}
        <View style={[styles.accentBorder, { backgroundColor: typeInfo.color }]} />
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: SPACING.lg,
    marginVertical: SPACING.sm,
    borderRadius: 20,
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
  },
  gradient: {
    borderRadius: 20,
    overflow: 'hidden',
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: COLORS.gray[100],
  },
  content: {
    padding: SPACING.lg,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.sm,
  },
  titleContainer: {
    flex: 1,
    marginRight: SPACING.sm,
  },
  title: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '700',
    color: COLORS.gray[900],
    marginBottom: SPACING.sm,
    lineHeight: 24,
  },
  typeBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    backgroundColor: COLORS.gray[100],
  },
  typeText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: '600',
    color: COLORS.gray[600],
    textTransform: 'capitalize',
  },
  dateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  dateInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  dateText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginLeft: SPACING.xs,
  },
  relativeDate: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
  },
  countryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  countryText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[500],
    marginLeft: SPACING.xs,
    fontWeight: '500',
  },
  description: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    lineHeight: 20,
    marginBottom: SPACING.sm,
  },
  activitiesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activitiesText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[500],
    marginLeft: SPACING.xs,
    flex: 1,
  },
  accentBorder: {
    height: 4,
    width: '100%',
  },
});

export default HolidayCard;
