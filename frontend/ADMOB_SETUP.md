# AdMob Setup Guide for Holivia App

## 1. Create AdMob Account
1. Go to [Google AdMob](https://admob.google.com/)
2. Sign in with your Google account
3. Create a new app or add existing app
4. Get your App ID

## 2. Configure App.json/App.config.js
Add your AdMob App ID to your Expo configuration:

```json
{
  "expo": {
    "plugins": [
      [
        "react-native-google-mobile-ads",
        {
          "android_app_id": "ca-app-pub-XXXXXXXXXXXXXXXX~XXXXXXXXXX",
          "ios_app_id": "ca-app-pub-XXXXXXXXXXXXXXXX~XXXXXXXXXX"
        }
      ]
    ]
  }
}
```

## 3. Create Ad Units in AdMob Console
Create the following ad units in your AdMob console:

### Banner Ads
- **Name**: Holivia Banner
- **Format**: Banner (320x50)
- **Placement**: Home screen, Holiday list

### Interstitial Ads
- **Name**: Holivia Interstitial
- **Format**: Interstitial
- **Placement**: Between holiday detail views

### Rewarded Ads (Optional)
- **Name**: Holivia Rewarded
- **Format**: Rewarded
- **Placement**: Unlock premium features

## 4. Update Ad Unit IDs
Replace the placeholder IDs in `src/services/adService.ts`:

```typescript
const AD_UNIT_IDS = {
  banner: __DEV__ 
    ? TestIds.BANNER 
    : Platform.OS === 'ios' 
      ? 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX' // Your iOS banner ad unit ID
      : 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX', // Your Android banner ad unit ID
  
  interstitial: __DEV__ 
    ? TestIds.INTERSTITIAL 
    : Platform.OS === 'ios' 
      ? 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX' // Your iOS interstitial ad unit ID
      : 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX', // Your Android interstitial ad unit ID
  
  rewarded: __DEV__ 
    ? TestIds.REWARDED 
    : Platform.OS === 'ios' 
      ? 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX' // Your iOS rewarded ad unit ID
      : 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX', // Your Android rewarded ad unit ID
};
```

## 5. Revenue Optimization Tips

### Ad Placement Strategy
- **Banner Ads**: Place at natural break points (between sections)
- **Interstitial Ads**: Show after user completes an action (viewing 3-4 holidays)
- **Native Ads**: Integrate seamlessly within content lists

### Expected Revenue (estimates)
- **Banner Ads**: $0.50-$2.00 per 1000 impressions
- **Interstitial Ads**: $1.00-$5.00 per 1000 impressions
- **Rewarded Ads**: $10.00-$25.00 per 1000 views

### Best Practices
1. **Don't oversaturate**: Too many ads hurt user experience
2. **Test ad frequency**: Monitor user retention vs. revenue
3. **Use mediation**: Consider Facebook Audience Network, Unity Ads
4. **A/B test placements**: Test different ad positions
5. **Respect user experience**: Ads should feel natural, not intrusive

## 6. Compliance & Privacy
- Add privacy policy mentioning ad personalization
- Implement GDPR consent if targeting EU users
- Follow Google AdMob policies
- Consider offering ad-free premium version

## 7. Monitoring & Analytics
- Set up Google Analytics for Firebase
- Track ad performance metrics
- Monitor user retention after ad implementation
- Optimize based on data

## 8. Alternative Monetization
Consider these additional revenue streams:
- **Premium subscription**: $2.99/month for ad-free experience
- **In-app purchases**: Premium themes, custom reminders
- **Affiliate marketing**: Holiday-related products
- **Sponsored content**: Partner with tourism boards

## 9. Build & Deploy
After setup:
```bash
expo prebuild
expo run:ios
expo run:android
```

## 10. Testing
- Test with AdMob test ads in development
- Verify ads load correctly on both platforms
- Test ad frequency and user experience
- Monitor crash reports related to ads
