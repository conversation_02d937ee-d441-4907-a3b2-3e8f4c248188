# Testing Guide for Holivia App

## 🗓️ Testing Month-End Reminders

### Method 1: Change Device Date (Recommended)
1. **Go to device Settings** → General → Date & Time
2. **Turn off "Set Automatically"**
3. **Set date to near month end** (e.g., January 29, 2024)
4. **Open Holivia app**
5. **Check HomeScreen** - you should see the month-end reminder card
6. **Test different reminder days** by setting dates like:
   - January 30 (1 day before month end)
   - January 28 (3 days before month end)
   - January 24 (7 days before month end)

### Method 2: Modify Code for Testing
Temporarily modify `getCurrentMonthEndInfo()` in `utils/index.ts`:

```typescript
export const getCurrentMonthEndInfo = (): MonthEndInfo => {
  const today = new Date();
  // Force test scenario - 3 days before month end
  const testDate = new Date(today.getFullYear(), today.getMonth() + 1, -2);
  return getMonthEndInfo(testDate.getFullYear(), testDate.getMonth() + 1);
};
```

### Expected Behavior
- **Month-end card appears** on HomeScreen when within 30 days of month end
- **Countdown shows correct days** remaining
- **Message updates** based on days remaining:
  - "Today is the last day of January!"
  - "Tomorrow is the last day of January!"
  - "3 days until the end of January"

## 📱 Testing Ads (Expo Go Limitations)

### Current Status
- **Expo Go**: Shows placeholder ads with "📱 Ad Placeholder (Expo Go)" text
- **Production Build**: Will show real AdMob ads

### Testing Real Ads
To test real ads, you need to create a production build:

```bash
# Create production build
expo prebuild
expo run:ios --configuration Release
# or
expo run:android --variant release
```

### Ad Placement Verification
Check these locations for ad placeholders:
1. **HomeScreen**: 
   - Between month-end reminder and quick actions
   - At bottom of screen
2. **HolidaysScreen**: After filter section
3. **HolidayDetailScreen**: Before action buttons

## 🔧 Troubleshooting

### Month-End Reminders Not Showing
1. **Check date**: Ensure you're within 30 days of month end
2. **Check preferences**: Verify notifications are enabled in Settings
3. **Check console**: Look for month-end related logs
4. **Restart app**: Force close and reopen

### Ad Errors in Expo Go
- **Expected behavior**: Ads won't work in Expo Go
- **Solution**: Use production build for real ad testing
- **Development**: Placeholder ads show the layout

### Notification Issues
1. **Check permissions**: Ensure notification permissions are granted
2. **Check settings**: Verify reminder days are set (1, 7, etc.)
3. **Test notification**: Use "Test Notification" in Settings

## 📊 Performance Testing

### Memory Usage
- Monitor app performance with ads enabled
- Check for memory leaks during ad loading
- Test app stability with frequent ad requests

### User Experience
- Verify ads don't interfere with app functionality
- Test ad loading times
- Ensure smooth scrolling with banner ads

## 🚀 Production Checklist

Before releasing to production:

### AdMob Setup
- [ ] Create AdMob account
- [ ] Add real ad unit IDs to `adService.ts`
- [ ] Configure app.json with AdMob app IDs
- [ ] Test with real ads in production build

### Month-End Reminders
- [ ] Test across different months
- [ ] Verify notifications work correctly
- [ ] Test preference changes update reminders
- [ ] Verify reminders work after app restart

### General
- [ ] Test on both iOS and Android
- [ ] Verify all ad placements work
- [ ] Check app performance with ads
- [ ] Test offline behavior
- [ ] Verify GDPR compliance (if needed)

## 🎯 Success Metrics

### Month-End Reminders
- Users see month-end countdown on HomeScreen
- Notifications trigger at correct times
- Preferences control both holiday and month-end reminders

### Monetization
- Ads load successfully in production
- No crashes related to ad loading
- Good user experience with non-intrusive ad placement
- Revenue tracking works correctly

## 📝 Notes

- **Development**: Use test ad units and placeholders
- **Production**: Replace with real ad unit IDs
- **Testing**: Always test on real devices, not just simulators
- **Monitoring**: Set up crash reporting and analytics
