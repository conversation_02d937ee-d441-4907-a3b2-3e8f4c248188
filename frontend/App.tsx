import React, { useState, useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider, useSafeAreaInsets } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';
import { View, Text, Image, StyleSheet } from 'react-native';
import { STORAGE_KEYS } from './src/constants';
import notificationService from './src/services/notificationService';
// import adService from './src/services/adService'; // Temporarily disabled for debugging

// Import screens
import HomeScreen from './src/screens/HomeScreen';
import CalendarScreen from './src/screens/CalendarScreen';
import HolidaysScreen from './src/screens/HolidaysScreen';
import SearchScreen from './src/screens/SearchScreen';
import SettingsScreen from './src/screens/SettingsScreen';
import HolidayDetailScreen from './src/screens/HolidayDetailScreen';
import SplashScreen from './src/screens/SplashScreen';
import OnboardingScreen from './src/screens/OnboardingScreen';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Custom header component with logo
const CustomHeader = ({ title }: { title: string }) => (
  <View style={headerStyles.container}>
    <Image
      source={require('./assets/logo.png')}
      style={headerStyles.logo}
      resizeMode="contain"
    />
    <Text style={headerStyles.title}>{title}</Text>
  </View>
);

const headerStyles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  logo: {
    width: 24,
    height: 24,
    marginRight: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
});

// Create the main tab navigator
function MainTabNavigator() {
  const insets = useSafeAreaInsets();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Calendar') {
            iconName = focused ? 'calendar' : 'calendar-outline';
          } else if (route.name === 'Holidays') {
            iconName = focused ? 'gift' : 'gift-outline';
          } else if (route.name === 'Search') {
            iconName = focused ? 'search' : 'search-outline';
          } else if (route.name === 'Settings') {
            iconName = focused ? 'settings' : 'settings-outline';
          } else {
            iconName = 'home-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#16a34a',
        tabBarInactiveTintColor: 'gray',
        tabBarStyle: {
          backgroundColor: 'white',
          borderTopWidth: 1,
          borderTopColor: '#e5e7eb',
          paddingBottom: Math.max(insets.bottom, 10), // Use safe area bottom or minimum 10
          paddingTop: 12,
          height: 60 + Math.max(insets.bottom, 10), // Adjust height based on safe area
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
        },
        headerStyle: {
          backgroundColor: '#16a34a',
        },
        headerTintColor: 'white',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
        <Tab.Screen
          name="Home"
          component={HomeScreen}
          options={{
            title: 'Holivia',
            headerTitle: () => <CustomHeader title="Holivia - Holiday Reminders" />,
          }}
        />
        <Tab.Screen
          name="Calendar"
          component={CalendarScreen}
          options={{
            title: 'Calendar',
            headerTitle: () => <CustomHeader title="Holiday Calendar" />,
          }}
        />
        <Tab.Screen
          name="Holidays"
          component={HolidaysScreen}
          options={{
            title: 'Holidays',
            headerTitle: () => <CustomHeader title="All Holidays" />,
          }}
        />
        <Tab.Screen
          name="Search"
          component={SearchScreen}
          options={{
            title: 'Search',
            headerTitle: () => <CustomHeader title="Search Holidays" />,
          }}
        />
        <Tab.Screen
          name="Settings"
          component={SettingsScreen}
          options={{
            title: 'Settings',
            headerTitle: () => <CustomHeader title="Settings" />,
          }}
        />
      </Tab.Navigator>
    );
}

export default function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [showOnboarding, setShowOnboarding] = useState(false);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Initialize notification service
      await notificationService.requestPermissions();

      // Initialize ad service (temporarily disabled for debugging)
      // await adService.initialize();

      // Check if onboarding is completed
      const onboardingCompleted = await AsyncStorage.getItem(STORAGE_KEYS.ONBOARDING_COMPLETED);

      if (!onboardingCompleted) {
        setShowOnboarding(true);
      } else {
        // Schedule month-end notifications if onboarding is completed
        await notificationService.scheduleMonthEndNotifications();
      }

      // Set up notification listeners
      const notificationListener = Notifications.addNotificationReceivedListener(
        notificationService.handleNotificationReceived
      );

      const responseListener = Notifications.addNotificationResponseReceivedListener(
        notificationService.handleNotificationResponse
      );

      // Cleanup listeners on unmount
      return () => {
        notificationListener.remove();
        responseListener.remove();
      };
    } catch (error) {
      console.error('Error initializing app:', error);
    }
  };

  const handleSplashFinish = () => {
    setIsLoading(false);
  };

  const handleOnboardingComplete = async () => {
    setShowOnboarding(false);
    // Schedule month-end notifications after onboarding
    try {
      await notificationService.scheduleMonthEndNotifications();
    } catch (error) {
      console.error('Error scheduling notifications after onboarding:', error);
    }
  };

  if (isLoading) {
    return <SplashScreen onFinish={handleSplashFinish} />;
  }

  if (showOnboarding) {
    return <OnboardingScreen onComplete={handleOnboardingComplete} />;
  }

  return (
    <SafeAreaProvider>
      <NavigationContainer>
        <StatusBar style="light" />
        <Stack.Navigator
          screenOptions={{
            headerStyle: {
              backgroundColor: '#16a34a',
            },
            headerTintColor: 'white',
            headerTitleStyle: {
              fontWeight: 'bold',
            },
          }}
        >
          <Stack.Screen
            name="MainTabs"
            component={MainTabNavigator}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="HolidayDetail"
            component={HolidayDetailScreen}
            options={{
              title: 'Holiday Details',
              headerTitle: () => <CustomHeader title="🎉 Holiday Details" />,
            }}
          />
        </Stack.Navigator>
      </NavigationContainer>
    </SafeAreaProvider>
  );
}
