{"expo": {"name": "Holivia", "slug": "holivia-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "scheme": "holivia", "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#22c55e"}, "updates": {"fallbackToCacheTimeout": 0}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.holivia.app", "infoPlist": {"UIBackgroundModes": ["background-fetch", "remote-notification"]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.holivia.app", "permissions": ["RECEIVE_BOOT_COMPLETED", "VIBRATE", "WAKE_LOCK"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["expo-notifications", {"icon": "./assets/icon.png", "color": "#22c55e", "defaultChannel": "default"}], ["react-native-google-mobile-ads", {"android_app_id": "ca-app-pub-3940256099942544~3347511713", "ios_app_id": "ca-app-pub-3940256099942544~1458002511"}]], "notification": {"icon": "./assets/icon.png", "color": "#22c55e", "androidMode": "default", "androidCollapsedTitle": "<PERSON> Reminder"}, "extra": {"eas": {"projectId": "295ddb45-54f6-4f82-90bf-ab5c39a3947d"}}}}