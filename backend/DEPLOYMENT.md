# Holivia Backend - Vercel Deployment Guide

## Overview
This backend is configured to deploy on Vercel as serverless functions. Each API route is deployed as a separate serverless function for optimal performance and scalability.

## Project Structure
```
backend/
├── api/                    # Vercel serverless functions
│   ├── _middleware.ts      # Shared middleware and utilities
│   ├── index.ts           # Main API handler (fallback)
│   ├── holidays.ts        # Holiday routes handler
│   ├── admin.ts           # Admin routes handler
│   ├── users.ts           # User routes handler
│   ├── notifications.ts   # Notification routes handler
│   └── health.ts          # Health check handler
├── src/                   # Source code (not deployed)
│   ├── controllers/       # Route controllers
│   ├── models/           # Database models
│   ├── routes/           # Express routes
│   ├── config/           # Configuration files
│   └── utils/            # Utility functions
├── vercel.json           # Vercel configuration
└── package.json          # Dependencies and scripts
```

## Environment Variables
Set these environment variables in your Vercel dashboard:

```
MONGODB_URI=mongodb+srv://username:<EMAIL>/holivia
NODE_ENV=production
FRONTEND_URL=https://your-frontend-domain.com
JWT_SECRET=your-jwt-secret-key
```

## Deployment Steps

### 1. Install Vercel CLI
```bash
npm install -g vercel
```

### 2. Login to Vercel
```bash
vercel login
```

### 3. Deploy from backend directory
```bash
cd backend
vercel --prod
```

### 4. Set Environment Variables
After deployment, set your environment variables in the Vercel dashboard or via CLI:
```bash
vercel env add MONGODB_URI
vercel env add NODE_ENV
vercel env add FRONTEND_URL
vercel env add JWT_SECRET
```

## API Endpoints
After deployment, your API will be available at:
- `https://your-project.vercel.app/api/holidays`
- `https://your-project.vercel.app/api/admin`
- `https://your-project.vercel.app/api/users`
- `https://your-project.vercel.app/api/notifications`
- `https://your-project.vercel.app/health`

## Features
- ✅ Serverless functions for each API route
- ✅ MongoDB connection pooling optimized for serverless
- ✅ CORS configured for your frontend
- ✅ Rate limiting
- ✅ Security headers with Helmet
- ✅ Error handling
- ✅ Health check endpoint

## Local Development
To run locally:
```bash
npm install
npm run dev
```

## Troubleshooting
1. **MongoDB Connection Issues**: Ensure your MongoDB URI is correct and your IP is whitelisted
2. **CORS Issues**: Add your frontend domain to the CORS configuration in `_middleware.ts`
3. **Function Timeout**: Vercel has a 10-second timeout for Hobby plan, 60 seconds for Pro
4. **Cold Starts**: First request after inactivity may be slower due to cold starts

## Monitoring
- Check function logs in Vercel dashboard
- Monitor function performance and errors
- Set up alerts for critical issues
