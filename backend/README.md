# Holivia Backend API

Backend API for Holivia - Local Public Holiday Reminder App

## Features

- RESTful API for holiday management
- MongoDB integration with Mongoose
- TypeScript for type safety
- Input validation with express-validator
- Security middleware (Helmet, CORS, Rate limiting)
- Comprehensive Nigerian holiday dataset
- Admin endpoints for holiday management

## Tech Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose
- **Language**: TypeScript
- **Validation**: express-validator
- **Security**: Helmet, CORS, express-rate-limit

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- MongoDB (local or cloud instance)
- npm or yarn

### Installation

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. Start MongoDB (if running locally)

4. Seed the database with Nigerian holidays:
```bash
npm run seed
```

5. Start the development server:
```bash
npm run dev
```

The API will be available at `http://localhost:5000`

## API Endpoints

### Public Endpoints

- `GET /health` - Health check
- `GET /api/holidays` - Get all holidays (with filtering)
- `GET /api/holidays/upcoming` - Get upcoming holidays
- `GET /api/holidays/search` - Search holidays
- `GET /api/holidays/:year/:month/:country` - Get holidays by month
- `GET /api/holidays/:id` - Get holiday by ID

### Admin Endpoints

- `GET /api/admin/stats` - Get admin statistics
- `POST /api/admin/holidays` - Create a new holiday
- `PUT /api/admin/holidays/:id` - Update a holiday
- `DELETE /api/admin/holidays/:id` - Delete a holiday
- `PATCH /api/admin/holidays/:id/toggle` - Toggle holiday status
- `POST /api/admin/holidays/bulk` - Bulk create holidays

## Query Parameters

### GET /api/holidays

- `country` - Filter by country (default: Nigeria)
- `year` - Filter by year
- `type` - Filter by type (national, religious, cultural, regional)
- `month` - Filter by month (1-12)
- `upcoming` - Show only upcoming holidays (true/false)
- `page` - Page number for pagination (default: 1)
- `limit` - Number of results per page (default: 20)
- `search` - Search in name and description

### GET /api/holidays/upcoming

- `country` - Filter by country (default: Nigeria)
- `limit` - Number of results (default: 5)

### GET /api/holidays/search

- `q` - Search query (required)
- `country` - Filter by country (default: Nigeria)
- `limit` - Number of results (default: 10)

## Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build TypeScript to JavaScript
- `npm start` - Start production server
- `npm run seed` - Seed database with Nigerian holidays

## Environment Variables

```env
PORT=5000
NODE_ENV=development
MONGODB_URI=mongodb://localhost:27017/holivia
FRONTEND_URL=http://localhost:3000
JWT_SECRET=your-secret-key
JWT_EXPIRE=7d
API_VERSION=v1
```

## Holiday Data Structure

```typescript
{
  name: string;
  date: Date;
  year: number;
  country: string;
  type: 'national' | 'religious' | 'cultural' | 'regional';
  description: string;
  history?: string;
  activities?: string[];
  isRecurring: boolean;
  recurringPattern?: 'yearly' | 'lunar' | 'custom';
  imageUrl?: string;
  isActive: boolean;
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License
