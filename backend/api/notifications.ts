import { VercelRequest, VercelResponse } from '@vercel/node';
import { createApp, ensureConnection } from './_middleware';
import notificationRoutes from '../src/routes/notificationRoutes';

const app = createApp();

// Use notification routes
app.use('/api/notifications', notificationRoutes);

export default async function handler(req: VercelRequest, res: VercelResponse) {
  await ensureConnection();
  app(req, res);
}