// This file is used as the Vercel serverless function entry point
import { VercelRequest, VercelResponse } from '@vercel/node';
import { createApp, ensureConnection } from './_middleware';
import holidayRoutes from '../src/routes/holidayRoutes';
import adminRoutes from '../src/routes/adminRoutes';
import userRoutes from '../src/routes/userRoutes';
import notificationRoutes from '../src/routes/notificationRoutes';

const app = createApp();

// Health check endpoint
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'Holivia API is running on Vercel',
    timestamp: new Date().toISOString(),
    environment: 'serverless'
  });
});

// API routes
app.use('/api/holidays', holidayRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/users', userRoutes);
app.use('/api/notifications', notificationRoutes);

// 404 handler
app.use('*', (_req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

export default async function handler(req: VercelRequest, res: VercelResponse) {
  await ensureConnection();
  app(req, res);
}
