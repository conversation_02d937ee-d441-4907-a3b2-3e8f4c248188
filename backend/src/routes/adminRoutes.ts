import express from 'express';
import { body } from 'express-validator';
import {
  createHoliday,
  updateHoliday,
  deleteHoliday,
  toggleHolidayStatus,
  bulkCreateHolidays,
  getAdminStats
} from '../controllers/adminController';

const router = express.Router();

// Validation rules for holiday creation/update
const holidayValidation = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Name must be between 1 and 100 characters'),
  body('date')
    .isISO8601()
    .withMessage('Date must be a valid ISO 8601 date'),
  body('year')
    .isInt({ min: 2020, max: 2030 })
    .withMessage('Year must be between 2020 and 2030'),
  body('country')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Country must be between 1 and 50 characters'),
  body('type')
    .isIn(['national', 'religious', 'cultural', 'regional'])
    .withMessage('Type must be one of: national, religious, cultural, regional'),
  body('description')
    .trim()
    .isLength({ min: 1, max: 500 })
    .withMessage('Description must be between 1 and 500 characters'),
  body('history')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('History cannot exceed 1000 characters'),
  body('activities')
    .optional()
    .isArray()
    .withMessage('Activities must be an array'),
  body('activities.*')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Each activity cannot exceed 200 characters'),
  body('isRecurring')
    .optional()
    .isBoolean()
    .withMessage('isRecurring must be a boolean'),
  body('recurringPattern')
    .optional()
    .isIn(['yearly', 'lunar', 'custom'])
    .withMessage('Recurring pattern must be one of: yearly, lunar, custom'),
  body('imageUrl')
    .optional()
    .isURL()
    .withMessage('Image URL must be a valid URL'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean')
];

// GET /api/admin/stats - Get admin statistics
router.get('/stats', getAdminStats);

// POST /api/admin/holidays - Create a new holiday
router.post('/holidays', holidayValidation, createHoliday);

// PUT /api/admin/holidays/:id - Update a holiday
router.put('/holidays/:id', holidayValidation, updateHoliday);

// DELETE /api/admin/holidays/:id - Delete a holiday
router.delete('/holidays/:id', deleteHoliday);

// PATCH /api/admin/holidays/:id/toggle - Toggle holiday active status
router.patch('/holidays/:id/toggle', toggleHolidayStatus);

// POST /api/admin/holidays/bulk - Bulk create holidays
router.post('/holidays/bulk', [
  body('holidays')
    .isArray({ min: 1 })
    .withMessage('Holidays array is required and cannot be empty'),
  body('holidays.*.name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Each holiday name must be between 1 and 100 characters'),
  body('holidays.*.date')
    .isISO8601()
    .withMessage('Each holiday date must be a valid ISO 8601 date'),
  body('holidays.*.year')
    .isInt({ min: 2020, max: 2030 })
    .withMessage('Each holiday year must be between 2020 and 2030'),
  body('holidays.*.type')
    .isIn(['national', 'religious', 'cultural', 'regional'])
    .withMessage('Each holiday type must be one of: national, religious, cultural, regional'),
  body('holidays.*.description')
    .trim()
    .isLength({ min: 1, max: 500 })
    .withMessage('Each holiday description must be between 1 and 500 characters')
], bulkCreateHolidays);

export default router;
