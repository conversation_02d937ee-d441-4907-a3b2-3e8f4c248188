import express from 'express';
import {
  getHolidays,
  getUpcomingHolidays,
  getHolidayById,
  getHolidaysByMonth,
  searchHolidays,
  getCountries,
  addCountryHolidays,
  addPredefinedCountry
} from '../controllers/holidayController';

const router = express.Router();

// GET /api/holidays - Get all holidays with filtering
router.get('/', getHolidays);

// GET /api/holidays/upcoming - Get upcoming holidays
router.get('/upcoming', getUpcomingHolidays);

// GET /api/holidays/search - Search holidays
router.get('/search', searchHolidays);

// GET /api/holidays/countries - Get available countries
router.get('/countries', getCountries);

// POST /api/holidays/countries - Add holidays for new country (Admin)
router.post('/countries', addCountryHolidays);

// POST /api/holidays/countries/predefined - Add predefined country with full data (Admin)
router.post('/countries/predefined', addPredefinedCountry);

// GET /api/holidays/by-month/:year/:month/:country - Get holidays by month
router.get('/by-month/:year/:month/:country', getHolidaysByMonth);

// GET /api/holidays/:id - Get holiday by ID (must be last)
router.get('/:id', getHolidayById);

export default router;
