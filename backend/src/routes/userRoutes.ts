import express from 'express';
import { body, param } from 'express-validator';
import {
  getUserPreferences,
  updateUserPreferences,
  getMonthEndSettings,
  syncUserData,
  deleteUserData
} from '../controllers/userController';

const router = express.Router();

// Validation middleware for device ID
const validateDeviceId = [
  param('deviceId')
    .isLength({ min: 1, max: 100 })
    .withMessage('Device ID must be between 1 and 100 characters')
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Device ID can only contain alphanumeric characters, hyphens, and underscores')
];

// Validation middleware for preferences
const validatePreferences = [
  body('notifications')
    .optional()
    .isBoolean()
    .withMessage('Notifications must be a boolean'),
  body('reminderDays')
    .optional()
    .isArray()
    .withMessage('Reminder days must be an array')
    .custom((value) => {
      if (Array.isArray(value)) {
        const validDays = value.every(day => 
          typeof day === 'number' && day >= 0 && day <= 365
        );
        if (!validDays) {
          throw new Error('All reminder days must be numbers between 0 and 365');
        }
      }
      return true;
    }),
  body('selectedCountry')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Selected country must be between 1 and 50 characters'),
  body('darkMode')
    .optional()
    .isBoolean()
    .withMessage('Dark mode must be a boolean'),
  body('autoRefresh')
    .optional()
    .isBoolean()
    .withMessage('Auto refresh must be a boolean'),
  body('monthEndReminders')
    .optional()
    .isBoolean()
    .withMessage('Month end reminders must be a boolean'),
  body('sound')
    .optional()
    .isBoolean()
    .withMessage('Sound must be a boolean'),
  body('vibration')
    .optional()
    .isBoolean()
    .withMessage('Vibration must be a boolean')
];

// Validation middleware for user sync
const validateUserSync = [
  body('email')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid email address'),
  body('name')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Name must be between 1 and 100 characters')
];

/**
 * @route   GET /api/users/:deviceId/preferences
 * @desc    Get user preferences by device ID
 * @access  Public
 */
router.get('/:deviceId/preferences', validateDeviceId, getUserPreferences);

/**
 * @route   PUT /api/users/:deviceId/preferences
 * @desc    Update user preferences
 * @access  Public
 */
router.put('/:deviceId/preferences', [...validateDeviceId, ...validatePreferences], updateUserPreferences);

/**
 * @route   GET /api/users/:deviceId/month-end-settings
 * @desc    Get month-end reminder settings
 * @access  Public
 */
router.get('/:deviceId/month-end-settings', validateDeviceId, getMonthEndSettings);

/**
 * @route   POST /api/users/:deviceId/sync
 * @desc    Sync user data (email, name)
 * @access  Public
 */
router.post('/:deviceId/sync', [...validateDeviceId, ...validateUserSync], syncUserData);

/**
 * @route   DELETE /api/users/:deviceId
 * @desc    Delete user data (GDPR compliance)
 * @access  Public
 */
router.delete('/:deviceId', validateDeviceId, deleteUserData);

export default router;
