import express from 'express';
import { param, body } from 'express-validator';
import {
  getUserNotifications,
  getLatestNotifications,
  markNotificationRead,
  createNotification,
  getAllNotifications
} from '../controllers/notificationController';

const router = express.Router();

// Validation middleware for device ID
const validateDeviceId = [
  param('deviceId')
    .isLength({ min: 1, max: 100 })
    .withMessage('Device ID must be between 1 and 100 characters')
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Device ID can only contain alphanumeric characters, hyphens, and underscores')
];

/**
 * @route   GET /api/notifications/:deviceId
 * @desc    Get notifications for user with pagination
 * @access  Public
 */
router.get('/:deviceId', validateDeviceId, getUserNotifications);

/**
 * @route   GET /api/notifications/:deviceId/latest
 * @desc    Get latest notifications for user
 * @access  Public
 */
router.get('/:deviceId/latest', validateDeviceId, getLatestNotifications);

/**
 * @route   POST /api/notifications/:notificationId/read
 * @desc    Mark notification as read
 * @access  Public
 */
router.post('/:notificationId/read', markNotificationRead);

/**
 * @route   POST /api/notifications
 * @desc    Create notification (Admin)
 * @access  Admin
 */
router.post('/', createNotification);

/**
 * @route   GET /api/notifications
 * @desc    Get all notifications (Admin)
 * @access  Admin
 */
router.get('/', getAllNotifications);

export default router;
