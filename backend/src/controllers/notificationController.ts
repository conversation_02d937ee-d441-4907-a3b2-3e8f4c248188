import { Request, Response } from 'express';
import Notification from '../models/Notification';

// Get notifications for user
export const getUserNotifications = async (req: Request, res: Response): Promise<void> => {
  try {
    const { deviceId } = req.params;
    const { page = 1, limit = 20 } = req.query;

    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Get global notifications and user-specific notifications
    const notifications = await Notification.find({
      isActive: true,
      $or: [
        { isGlobal: true },
        { targetUsers: deviceId }
      ]
    })
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limitNum)
    .lean();

    const total = await Notification.countDocuments({
      isActive: true,
      $or: [
        { isGlobal: true },
        { targetUsers: deviceId }
      ]
    });

    res.status(200).json({
      success: true,
      data: notifications,
      pagination: {
        current: pageNum,
        pages: Math.ceil(total / limitNum),
        total,
        hasNext: pageNum * limitNum < total,
        hasPrev: pageNum > 1
      }
    });
  } catch (error) {
    console.error('Error fetching user notifications:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching notifications',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get latest notifications (for app startup)
export const getLatestNotifications = async (req: Request, res: Response): Promise<void> => {
  try {
    const { deviceId } = req.params;
    const { limit = 5 } = req.query;

    const notifications = await Notification.find({
      isActive: true,
      createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }, // Last 7 days
      $or: [
        { isGlobal: true },
        { targetUsers: deviceId }
      ]
    })
    .sort({ createdAt: -1 })
    .limit(parseInt(limit as string))
    .lean();

    res.status(200).json({
      success: true,
      data: notifications
    });
  } catch (error) {
    console.error('Error fetching latest notifications:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching latest notifications',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Mark notification as read (optional feature)
export const markNotificationRead = async (req: Request, res: Response): Promise<void> => {
  try {
    const { notificationId } = req.params;
    const { deviceId } = req.body;

    // For now, we'll just return success
    // In a full implementation, you might track read status per user
    res.status(200).json({
      success: true,
      message: 'Notification marked as read'
    });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    res.status(500).json({
      success: false,
      message: 'Error marking notification as read',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Admin: Create notification
export const createNotification = async (req: Request, res: Response): Promise<void> => {
  try {
    const { type, title, message, data, targetUsers, isGlobal = true } = req.body;

    if (!type || !title || !message) {
      res.status(400).json({
        success: false,
        message: 'Type, title, and message are required'
      });
      return;
    }

    const notification = new Notification({
      type,
      title,
      message,
      data,
      targetUsers: targetUsers || [],
      isGlobal,
      isActive: true
    });

    await notification.save();

    res.status(201).json({
      success: true,
      data: notification,
      message: 'Notification created successfully'
    });
  } catch (error) {
    console.error('Error creating notification:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating notification',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Admin: Get all notifications
export const getAllNotifications = async (req: Request, res: Response): Promise<void> => {
  try {
    const { page = 1, limit = 20, type } = req.query;

    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    const query: any = {};
    if (type) query.type = type;

    const notifications = await Notification.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNum)
      .lean();

    const total = await Notification.countDocuments(query);

    res.status(200).json({
      success: true,
      data: notifications,
      pagination: {
        current: pageNum,
        pages: Math.ceil(total / limitNum),
        total,
        hasNext: pageNum * limitNum < total,
        hasPrev: pageNum > 1
      }
    });
  } catch (error) {
    console.error('Error fetching all notifications:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching notifications',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
