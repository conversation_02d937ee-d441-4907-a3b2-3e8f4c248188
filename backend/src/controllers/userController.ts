import { Request, Response } from 'express';
import User, { IUser, IUserPreferences } from '../models/User';

// Get user preferences by device ID
export const getUserPreferences = async (req: Request, res: Response): Promise<void> => {
  try {
    const { deviceId } = req.params;
    
    if (!deviceId) {
      res.status(400).json({
        success: false,
        message: 'Device ID is required'
      });
      return;
    }

    const user = await User.findOne({ deviceId }).lean();
    
    if (!user) {
      // Return default preferences for new users
      const defaultPreferences: IUserPreferences = {
        notifications: true,
        reminderDays: [1, 7],
        selectedCountry: 'Nigeria',
        selectedCountries: ['Nigeria'],
        darkMode: false,
        autoRefresh: true,
        monthEndReminders: true,
        sound: true,
        vibration: true
      };

      res.status(200).json({
        success: true,
        data: {
          preferences: defaultPreferences,
          isNew: true
        }
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: {
        preferences: user.preferences,
        lastActive: user.lastActive,
        isNew: false
      }
    });
  } catch (error) {
    console.error('Error fetching user preferences:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching user preferences',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Update user preferences
export const updateUserPreferences = async (req: Request, res: Response): Promise<void> => {
  try {
    const { deviceId } = req.params;
    const preferences = req.body;
    
    if (!deviceId) {
      res.status(400).json({
        success: false,
        message: 'Device ID is required'
      });
      return;
    }

    // Validate preferences structure
    const validKeys = [
      'notifications', 'reminderDays', 'selectedCountry', 'darkMode', 
      'autoRefresh', 'monthEndReminders', 'sound', 'vibration'
    ];
    
    const invalidKeys = Object.keys(preferences).filter(key => !validKeys.includes(key));
    if (invalidKeys.length > 0) {
      res.status(400).json({
        success: false,
        message: `Invalid preference keys: ${invalidKeys.join(', ')}`
      });
      return;
    }

    // Validate reminderDays if provided
    if (preferences.reminderDays && Array.isArray(preferences.reminderDays)) {
      const invalidDays = preferences.reminderDays.filter((day: any) => 
        typeof day !== 'number' || day < 0 || day > 365
      );
      if (invalidDays.length > 0) {
        res.status(400).json({
          success: false,
          message: 'Reminder days must be numbers between 0 and 365'
        });
        return;
      }
    }

    // Find or create user
    let user = await User.findOne({ deviceId });
    
    if (!user) {
      user = new User({
        deviceId,
        preferences
      });
    } else {
      // Update preferences
      user.preferences = { ...user.preferences, ...preferences };
      user.lastActive = new Date();
    }

    await user.save();

    res.status(200).json({
      success: true,
      data: {
        preferences: user.preferences,
        lastActive: user.lastActive
      },
      message: 'Preferences updated successfully'
    });
  } catch (error) {
    console.error('Error updating user preferences:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating user preferences',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get month-end reminder settings
export const getMonthEndSettings = async (req: Request, res: Response): Promise<void> => {
  try {
    const { deviceId } = req.params;
    
    if (!deviceId) {
      res.status(400).json({
        success: false,
        message: 'Device ID is required'
      });
      return;
    }

    const user = await User.findOne({ deviceId }).lean();
    
    const monthEndSettings = {
      enabled: user?.preferences?.monthEndReminders ?? true,
      reminderDays: user?.preferences?.reminderDays ?? [1, 7],
      notifications: user?.preferences?.notifications ?? true
    };

    res.status(200).json({
      success: true,
      data: monthEndSettings
    });
  } catch (error) {
    console.error('Error fetching month-end settings:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching month-end settings',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Sync user data (for future use)
export const syncUserData = async (req: Request, res: Response): Promise<void> => {
  try {
    const { deviceId } = req.params;
    const { email, name } = req.body;
    
    if (!deviceId) {
      res.status(400).json({
        success: false,
        message: 'Device ID is required'
      });
      return;
    }

    let user = await User.findOne({ deviceId });
    
    if (!user) {
      res.status(404).json({
        success: false,
        message: 'User not found'
      });
      return;
    }

    // Update user info if provided
    if (email) user.email = email;
    if (name) user.name = name;
    user.lastActive = new Date();

    await user.save();

    res.status(200).json({
      success: true,
      data: {
        deviceId: user.deviceId,
        email: user.email,
        name: user.name,
        preferences: user.preferences,
        lastActive: user.lastActive,
        isAnonymous: !user.email
      },
      message: 'User data synced successfully'
    });
  } catch (error) {
    console.error('Error syncing user data:', error);
    res.status(500).json({
      success: false,
      message: 'Error syncing user data',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Delete user data (GDPR compliance)
export const deleteUserData = async (req: Request, res: Response): Promise<void> => {
  try {
    const { deviceId } = req.params;
    
    if (!deviceId) {
      res.status(400).json({
        success: false,
        message: 'Device ID is required'
      });
      return;
    }

    const result = await User.deleteOne({ deviceId });
    
    if (result.deletedCount === 0) {
      res.status(404).json({
        success: false,
        message: 'User not found'
      });
      return;
    }

    res.status(200).json({
      success: true,
      message: 'User data deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting user data:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting user data',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
