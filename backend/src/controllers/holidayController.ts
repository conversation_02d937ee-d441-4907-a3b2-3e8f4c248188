import { Request, Response } from 'express';
import Holiday, { IHoliday } from '../models/Holiday';
import Notification from '../models/Notification';
import { validationResult } from 'express-validator';

// Get all holidays with filtering and pagination
export const getHolidays = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      country = 'Nigeria',
      year,
      type,
      month,
      upcoming,
      page = 1,
      limit = 20,
      search
    } = req.query;

    // Build query - only show current year and future years
    const currentYear = new Date().getFullYear();
    const query: any = {
      isActive: true,
      year: { $gte: currentYear } // Only current year and future years
    };

    if (country) query.country = country;

    if (year) {
      const requestedYear = parseInt(year as string);
      if (requestedYear >= currentYear) {
        query.year = requestedYear;
      } else {
        // If requesting past year, return empty results
        res.status(200).json({
          success: true,
          data: [],
          meta: {
            total: 0,
            page: parseInt(page as string),
            limit: parseInt(limit as string),
            pages: 0
          }
        });
        return;
      }
    }

    if (type) query.type = type;
    
    if (month) {
      const monthNum = parseInt(month as string);
      query.date = {
        $gte: new Date(query.year || new Date().getFullYear(), monthNum - 1, 1),
        $lt: new Date(query.year || new Date().getFullYear(), monthNum, 1)
      };
    }
    
    if (upcoming === 'true') {
      query.date = { $gte: new Date() };
    }
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Pagination
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Execute query
    const holidays = await Holiday.find(query)
      .sort({ date: 1 })
      .skip(skip)
      .limit(limitNum)
      .lean();

    const total = await Holiday.countDocuments(query);

    res.status(200).json({
      success: true,
      data: holidays,
      pagination: {
        current: pageNum,
        pages: Math.ceil(total / limitNum),
        total,
        hasNext: pageNum < Math.ceil(total / limitNum),
        hasPrev: pageNum > 1
      }
    });
  } catch (error) {
    console.error('Error fetching holidays:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching holidays',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get upcoming holidays (next year from today)
export const getUpcomingHolidays = async (req: Request, res: Response): Promise<void> => {
  try {
    const { country = 'Nigeria', limit = 5 } = req.query;

    const today = new Date();
    const oneYearFromNow = new Date();
    oneYearFromNow.setFullYear(today.getFullYear() + 1);

    const holidays = await Holiday.find({
      country,
      isActive: true,
      date: {
        $gte: today
      }
    })
    .sort({ date: 1 })
    .limit(parseInt(limit as string))
    .lean();

    res.status(200).json({
      success: true,
      data: holidays
    });
  } catch (error) {
    console.error('Error fetching upcoming holidays:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching upcoming holidays',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get holiday by ID
export const getHolidayById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    const holiday = await Holiday.findById(id).lean();
    
    if (!holiday) {
      res.status(404).json({
        success: false,
        message: 'Holiday not found'
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: holiday
    });
  } catch (error) {
    console.error('Error fetching holiday:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching holiday',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get holidays by month
export const getHolidaysByMonth = async (req: Request, res: Response): Promise<void> => {
  try {
    const { year, month, country = 'Nigeria' } = req.params;
    
    const startDate = new Date(parseInt(year), parseInt(month) - 1, 1);
    const endDate = new Date(parseInt(year), parseInt(month), 0);

    const holidays = await Holiday.find({
      country,
      isActive: true,
      date: {
        $gte: startDate,
        $lte: endDate
      }
    })
    .sort({ date: 1 })
    .lean();

    res.status(200).json({
      success: true,
      data: holidays,
      meta: {
        year: parseInt(year),
        month: parseInt(month),
        country,
        count: holidays.length
      }
    });
  } catch (error) {
    console.error('Error fetching holidays by month:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching holidays by month',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Search holidays
export const searchHolidays = async (req: Request, res: Response): Promise<void> => {
  try {
    const { q, country = 'Nigeria', limit = 10 } = req.query;
    
    if (!q) {
      res.status(400).json({
        success: false,
        message: 'Search query is required'
      });
      return;
    }

    const holidays = await Holiday.find({
      country,
      isActive: true,
      $or: [
        { name: { $regex: q, $options: 'i' } },
        { description: { $regex: q, $options: 'i' } },
        { activities: { $elemMatch: { $regex: q, $options: 'i' } } }
      ]
    })
    .sort({ date: 1 })
    .limit(parseInt(limit as string))
    .lean();

    res.status(200).json({
      success: true,
      data: holidays,
      meta: {
        query: q,
        count: holidays.length
      }
    });
  } catch (error) {
    console.error('Error searching holidays:', error);
    res.status(500).json({
      success: false,
      message: 'Error searching holidays',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get available countries
export const getCountries = async (req: Request, res: Response): Promise<void> => {
  try {
    const countries = await Holiday.distinct('country', { isActive: true });

    res.status(200).json({
      success: true,
      data: countries.sort()
    });
  } catch (error) {
    console.error('Error fetching countries:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching countries',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Add holidays for new country (Admin only)
export const addCountryHolidays = async (req: Request, res: Response): Promise<void> => {
  try {
    const { country, holidays } = req.body;

    if (!country || !holidays || !Array.isArray(holidays)) {
      res.status(400).json({
        success: false,
        message: 'Country name and holidays array are required'
      });
      return;
    }

    // Check if country already exists
    const existingHolidays = await Holiday.findOne({ country, isActive: true });
    const isNewCountry = !existingHolidays;

    // Add holidays
    const holidayDocs = holidays.map(holiday => ({
      ...holiday,
      country,
      isActive: true
    }));

    const result = await Holiday.insertMany(holidayDocs);

    // If it's a new country, create a notification
    if (isNewCountry) {
      try {
        await (Notification as any).createCountryNotification([country]);
        console.log(`✅ Created notification for new country: ${country}`);
      } catch (notificationError) {
        console.error('Error creating country notification:', notificationError);
        // Don't fail the main operation if notification fails
      }
    }

    res.status(201).json({
      success: true,
      data: {
        country,
        holidaysAdded: result.length,
        isNewCountry,
        notificationCreated: isNewCountry
      },
      message: `Successfully added ${result.length} holidays for ${country}${isNewCountry ? '. Users will be notified!' : ''}`
    });
  } catch (error) {
    console.error('Error adding country holidays:', error);
    res.status(500).json({
      success: false,
      message: 'Error adding country holidays',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Add predefined country with full holiday data
export const addPredefinedCountry = async (req: Request, res: Response): Promise<void> => {
  try {
    const { countryName } = req.body;

    if (!countryName) {
      res.status(400).json({
        success: false,
        message: 'Country name is required'
      });
      return;
    }

    // Import country data
    const { countryHolidaysData } = await import('../data/countryHolidays');

    if (!countryHolidaysData[countryName as keyof typeof countryHolidaysData]) {
      res.status(404).json({
        success: false,
        message: `Country '${countryName}' not found in predefined data. Available countries: ${Object.keys(countryHolidaysData).join(', ')}`
      });
      return;
    }

    // Check if country already exists
    const existingHolidays = await Holiday.findOne({ country: countryName, isActive: true });
    const isNewCountry = !existingHolidays;

    if (!isNewCountry) {
      res.status(409).json({
        success: false,
        message: `Country '${countryName}' already exists in the database`
      });
      return;
    }

    // Get holiday data for the country
    const holidays = countryHolidaysData[countryName as keyof typeof countryHolidaysData];

    // Add holidays
    const holidayDocs = holidays.map(holiday => ({
      ...holiday,
      country: countryName,
      isActive: true
    }));

    const result = await Holiday.insertMany(holidayDocs);

    // Create notification for new country
    try {
      await (Notification as any).createCountryNotification([countryName]);
      console.log(`✅ Created notification for new country: ${countryName}`);
    } catch (notificationError) {
      console.error('Error creating country notification:', notificationError);
    }

    res.status(201).json({
      success: true,
      data: {
        country: countryName,
        holidaysAdded: result.length,
        isNewCountry: true,
        notificationCreated: true,
        holidays: result
      },
      message: `Successfully added ${countryName} with ${result.length} holidays. Users will be notified!`
    });
  } catch (error) {
    console.error('Error adding predefined country:', error);
    res.status(500).json({
      success: false,
      message: 'Error adding predefined country',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
