import { Request, Response } from 'express';
import Holiday, { IHoliday } from '../models/Holiday';
import { validationResult } from 'express-validator';

// Create a new holiday
export const createHoliday = async (req: Request, res: Response): Promise<void> => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
      return;
    }

    const holidayData = req.body;
    
    // Check if holiday already exists for the same date and country
    const existingHoliday = await Holiday.findOne({
      name: holidayData.name,
      date: holidayData.date,
      country: holidayData.country
    });

    if (existingHoliday) {
      res.status(409).json({
        success: false,
        message: 'Holiday already exists for this date and country'
      });
      return;
    }

    const holiday = new Holiday(holidayData);
    await holiday.save();

    res.status(201).json({
      success: true,
      message: 'Holiday created successfully',
      data: holiday
    });
  } catch (error) {
    console.error('Error creating holiday:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating holiday',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Update a holiday
export const updateHoliday = async (req: Request, res: Response): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
      return;
    }

    const { id } = req.params;
    const updateData = req.body;

    const holiday = await Holiday.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    );

    if (!holiday) {
      res.status(404).json({
        success: false,
        message: 'Holiday not found'
      });
      return;
    }

    res.status(200).json({
      success: true,
      message: 'Holiday updated successfully',
      data: holiday
    });
  } catch (error) {
    console.error('Error updating holiday:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating holiday',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Delete a holiday
export const deleteHoliday = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    const holiday = await Holiday.findByIdAndDelete(id);

    if (!holiday) {
      res.status(404).json({
        success: false,
        message: 'Holiday not found'
      });
      return;
    }

    res.status(200).json({
      success: true,
      message: 'Holiday deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting holiday:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting holiday',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Toggle holiday active status
export const toggleHolidayStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    const holiday = await Holiday.findById(id);

    if (!holiday) {
      res.status(404).json({
        success: false,
        message: 'Holiday not found'
      });
      return;
    }

    holiday.isActive = !holiday.isActive;
    await holiday.save();

    res.status(200).json({
      success: true,
      message: `Holiday ${holiday.isActive ? 'activated' : 'deactivated'} successfully`,
      data: holiday
    });
  } catch (error) {
    console.error('Error toggling holiday status:', error);
    res.status(500).json({
      success: false,
      message: 'Error toggling holiday status',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Bulk create holidays
export const bulkCreateHolidays = async (req: Request, res: Response): Promise<void> => {
  try {
    const { holidays } = req.body;

    if (!Array.isArray(holidays) || holidays.length === 0) {
      res.status(400).json({
        success: false,
        message: 'Holidays array is required and cannot be empty'
      });
      return;
    }

    const createdHolidays = await Holiday.insertMany(holidays, { ordered: false });

    res.status(201).json({
      success: true,
      message: `${createdHolidays.length} holidays created successfully`,
      data: createdHolidays
    });
  } catch (error) {
    console.error('Error bulk creating holidays:', error);
    res.status(500).json({
      success: false,
      message: 'Error bulk creating holidays',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get admin statistics
export const getAdminStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const currentYear = new Date().getFullYear();
    
    const [
      totalHolidays,
      activeHolidays,
      upcomingHolidays,
      holidaysByType,
      holidaysByMonth
    ] = await Promise.all([
      Holiday.countDocuments(),
      Holiday.countDocuments({ isActive: true }),
      Holiday.countDocuments({ 
        isActive: true, 
        date: { $gte: new Date() } 
      }),
      Holiday.aggregate([
        { $match: { isActive: true } },
        { $group: { _id: '$type', count: { $sum: 1 } } }
      ]),
      Holiday.aggregate([
        { 
          $match: { 
            isActive: true, 
            year: currentYear 
          } 
        },
        { 
          $group: { 
            _id: { $month: '$date' }, 
            count: { $sum: 1 } 
          } 
        },
        { $sort: { _id: 1 } }
      ])
    ]);

    res.status(200).json({
      success: true,
      data: {
        totalHolidays,
        activeHolidays,
        upcomingHolidays,
        holidaysByType,
        holidaysByMonth,
        year: currentYear
      }
    });
  } catch (error) {
    console.error('Error fetching admin stats:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching admin statistics',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
