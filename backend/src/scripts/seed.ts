import dotenv from 'dotenv';
import { connectDB } from '../config/database';
import { seedHolidays } from '../utils/seedData';

// Load environment variables
dotenv.config();

const runSeed = async (): Promise<void> => {
  try {
    console.log('🌱 Starting database seeding...');
    
    // Connect to database
    await connectDB();
    
    // Seed holidays
    await seedHolidays();
    
    console.log('✅ Database seeding completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    process.exit(1);
  }
};

runSeed();
