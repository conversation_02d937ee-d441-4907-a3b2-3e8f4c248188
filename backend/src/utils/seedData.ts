import Holiday from '../models/Holiday';
import { holidays2025 } from '../data/holidays2025';

export const nigerianHolidays2024 = [
  {
    name: "New Year's Day",
    date: new Date('2024-01-01'),
    year: 2024,
    country: 'Nigeria',
    type: 'national',
    description: 'The first day of the year in the Gregorian calendar, celebrated worldwide.',
    history: 'New Year\'s Day has been celebrated since ancient times, marking the beginning of a new year.',
    activities: [
      'Family gatherings and celebrations',
      'Making New Year resolutions',
      'Watching fireworks displays',
      'Attending church services'
    ],
    isRecurring: true,
    recurringPattern: 'yearly',
    isActive: true
  },
  {
    name: "Good Friday",
    date: new Date('2024-03-29'),
    year: 2024,
    country: 'Nigeria',
    type: 'religious',
    description: 'Christian holiday commemorating the crucifixion of Jesus Christ.',
    history: 'Good Friday is observed during Holy Week as part of the Paschal Triduum on the Friday preceding Easter Sunday.',
    activities: [
      'Attending church services',
      'Prayer and reflection',
      'Fasting and abstinence',
      'Stations of the Cross'
    ],
    isRecurring: true,
    recurringPattern: 'yearly',
    isActive: true
  },
  {
    name: "Easter Monday",
    date: new Date('2024-04-01'),
    year: 2024,
    country: 'Nigeria',
    type: 'religious',
    description: 'Christian holiday celebrating the resurrection of Jesus Christ.',
    history: 'Easter Monday is the day after Easter Sunday, extending the celebration of Christ\'s resurrection.',
    activities: [
      'Family gatherings',
      'Easter egg hunts',
      'Attending church services',
      'Sharing meals with loved ones'
    ],
    isRecurring: true,
    recurringPattern: 'yearly',
    isActive: true
  },
  {
    name: "Workers' Day",
    date: new Date('2024-05-01'),
    year: 2024,
    country: 'Nigeria',
    type: 'national',
    description: 'International Workers\' Day, also known as Labour Day, celebrating workers and their contributions.',
    history: 'May Day has its roots in the labor union movement, specifically the eight-hour day movement.',
    activities: [
      'Labor union rallies and parades',
      'Workers\' rights advocacy',
      'Community gatherings',
      'Rest and relaxation'
    ],
    isRecurring: true,
    recurringPattern: 'yearly',
    isActive: true
  },
  {
    name: "Children's Day",
    date: new Date('2024-05-27'),
    year: 2024,
    country: 'Nigeria',
    type: 'national',
    description: 'A day dedicated to celebrating children and promoting their welfare.',
    history: 'Children\'s Day in Nigeria is celebrated to honor children and raise awareness about children\'s rights.',
    activities: [
      'School celebrations and programs',
      'Children\'s parties and games',
      'Educational activities',
      'Family outings to parks and recreational centers'
    ],
    isRecurring: true,
    recurringPattern: 'yearly',
    isActive: true
  },
  {
    name: "Democracy Day",
    date: new Date('2024-06-12'),
    year: 2024,
    country: 'Nigeria',
    type: 'national',
    description: 'Commemorates Nigeria\'s return to democratic governance.',
    history: 'June 12 was declared Democracy Day to honor the presumed winner of the June 12, 1993 presidential election.',
    activities: [
      'Government ceremonies and speeches',
      'Civic education programs',
      'Cultural displays and parades',
      'Reflection on democratic values'
    ],
    isRecurring: true,
    recurringPattern: 'yearly',
    isActive: true
  },
  {
    name: "Eid al-Adha",
    date: new Date('2024-06-16'),
    year: 2024,
    country: 'Nigeria',
    type: 'religious',
    description: 'Islamic festival commemorating Abraham\'s willingness to sacrifice his son.',
    history: 'Also known as the Festival of Sacrifice, it honors the willingness of Ibrahim to sacrifice his son as an act of obedience to God.',
    activities: [
      'Special prayers at mosques',
      'Animal sacrifice (Qurbani)',
      'Sharing meals with family and community',
      'Giving to charity (Zakat)'
    ],
    isRecurring: true,
    recurringPattern: 'lunar',
    isActive: true
  },
  {
    name: "Independence Day",
    date: new Date('2024-10-01'),
    year: 2024,
    country: 'Nigeria',
    type: 'national',
    description: 'Celebrates Nigeria\'s independence from British colonial rule.',
    history: 'Nigeria gained independence on October 1, 1960, ending British colonial rule.',
    activities: [
      'National parade and ceremonies',
      'Flag hoisting ceremonies',
      'Cultural displays and performances',
      'Patriotic songs and speeches'
    ],
    isRecurring: true,
    recurringPattern: 'yearly',
    isActive: true
  },
  {
    name: "Christmas Day",
    date: new Date('2024-12-25'),
    year: 2024,
    country: 'Nigeria',
    type: 'religious',
    description: 'Christian holiday celebrating the birth of Jesus Christ.',
    history: 'Christmas commemorates the birth of Jesus Christ and is celebrated by Christians worldwide.',
    activities: [
      'Attending church services',
      'Family gatherings and feasts',
      'Gift giving and exchange',
      'Carol singing and decorations'
    ],
    isRecurring: true,
    recurringPattern: 'yearly',
    isActive: true
  },
  {
    name: "Boxing Day",
    date: new Date('2024-12-26'),
    year: 2024,
    country: 'Nigeria',
    type: 'national',
    description: 'The day after Christmas, traditionally for giving gifts to service workers.',
    history: 'Boxing Day originated in the United Kingdom and is observed in many Commonwealth countries.',
    activities: [
      'Visiting friends and extended family',
      'Shopping and sales',
      'Sports events and entertainment',
      'Relaxation and leisure activities'
    ],
    isRecurring: true,
    recurringPattern: 'yearly',
    isActive: true
  }
];

export const seedHolidays = async (): Promise<void> => {
  try {
    // Clear existing holidays for 2024 and 2025
    await Holiday.deleteMany({ year: { $in: [2024, 2025] } });

    // Insert 2024 holidays
    await Holiday.insertMany(nigerianHolidays2024);

    // Convert 2025 holidays data to proper format with Date objects
    const holidays2025WithDates = holidays2025.map(holiday => ({
      ...holiday,
      date: new Date(holiday.date)
    }));

    // Insert 2025 holidays
    await Holiday.insertMany(holidays2025WithDates);

    console.log('✅ Holidays for 2024 and 2025 seeded successfully');
    console.log(`✅ Seeded ${nigerianHolidays2024.length} holidays for 2024`);
    console.log(`✅ Seeded ${holidays2025.length} holidays for 2025`);
  } catch (error) {
    console.error('❌ Error seeding holidays:', error);
    throw error;
  }
};
