import mongoose, { Document, Schema } from 'mongoose';

export interface IUserPreferences {
  notifications: boolean;
  reminderDays: number[];
  selectedCountries: string[]; // Changed from selectedCountry to support multiple
  selectedCountry?: string; // Keep for backward compatibility
  darkMode: boolean;
  autoRefresh: boolean;
  monthEndReminders: boolean;
  sound: boolean;
  vibration: boolean;
}

export interface IUser extends Document {
  deviceId: string; // For anonymous users
  email?: string;
  name?: string;
  preferences: IUserPreferences;
  lastActive: Date;
  createdAt: Date;
  updatedAt: Date;
  isAnonymous: boolean; // Virtual property
}

const UserPreferencesSchema = new Schema({
  notifications: {
    type: Boolean,
    default: true
  },
  reminderDays: [{
    type: Number,
    validate: {
      validator: function(v: number) {
        return v >= 0 && v <= 365;
      },
      message: 'Reminder days must be between 0 and 365'
    }
  }],
  selectedCountries: [{
    type: String,
    trim: true
  }],
  selectedCountry: {
    type: String,
    default: 'Nigeria',
    trim: true
  },
  darkMode: {
    type: Boolean,
    default: false
  },
  autoRefresh: {
    type: Boolean,
    default: true
  },
  monthEndReminders: {
    type: Boolean,
    default: true
  },
  sound: {
    type: Boolean,
    default: true
  },
  vibration: {
    type: Boolean,
    default: true
  }
}, { _id: false });

const UserSchema: Schema = new Schema({
  deviceId: {
    type: String,
    required: [true, 'Device ID is required'],
    unique: true,
    trim: true
  },
  email: {
    type: String,
    trim: true,
    lowercase: true,
    validate: {
      validator: function(v: string) {
        return !v || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v);
      },
      message: 'Please enter a valid email address'
    }
  },
  name: {
    type: String,
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters']
  },
  preferences: {
    type: UserPreferencesSchema,
    default: () => ({})
  },
  lastActive: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
UserSchema.index({ deviceId: 1 });
UserSchema.index({ email: 1 });
UserSchema.index({ lastActive: 1 });

// Update lastActive on save
UserSchema.pre('save', function(next) {
  this.lastActive = new Date();
  next();
});

// Virtual for user type
UserSchema.virtual('isAnonymous').get(function(this: IUser) {
  return !this.email;
});

// Static method to find or create user by device ID
UserSchema.statics.findOrCreateByDeviceId = async function(deviceId: string, initialPreferences?: Partial<IUserPreferences>) {
  let user = await this.findOne({ deviceId });
  
  if (!user) {
    user = new this({
      deviceId,
      preferences: initialPreferences || {}
    });
    await user.save();
  }
  
  return user;
};

export default mongoose.model<IUser>('User', UserSchema);
