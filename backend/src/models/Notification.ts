import mongoose, { Document, Schema } from 'mongoose';

export interface INotification extends Document {
  type: 'country_added' | 'holiday_update' | 'app_update';
  title: string;
  message: string;
  data?: any;
  targetUsers?: string[]; // Device IDs, empty array means all users
  isGlobal: boolean;
  isActive: boolean;
  scheduledFor?: Date;
  sentAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const NotificationSchema: Schema = new Schema({
  type: {
    type: String,
    required: [true, 'Notification type is required'],
    enum: ['country_added', 'holiday_update', 'app_update'],
    index: true
  },
  title: {
    type: String,
    required: [true, 'Notification title is required'],
    trim: true,
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  message: {
    type: String,
    required: [true, 'Notification message is required'],
    trim: true,
    maxlength: [500, 'Message cannot exceed 500 characters']
  },
  data: {
    type: Schema.Types.Mixed,
    default: {}
  },
  targetUsers: [{
    type: String,
    trim: true
  }],
  isGlobal: {
    type: Boolean,
    default: true
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  scheduledFor: {
    type: Date,
    default: Date.now
  },
  sentAt: {
    type: Date
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
NotificationSchema.index({ type: 1, isActive: 1 });
NotificationSchema.index({ scheduledFor: 1, sentAt: 1 });
NotificationSchema.index({ isGlobal: 1, isActive: 1 });

// Virtual for status
NotificationSchema.virtual('status').get(function(this: INotification) {
  if (this.sentAt) return 'sent';
  if (this.scheduledFor && this.scheduledFor > new Date()) return 'scheduled';
  return 'pending';
});

// Static method to create country notification
NotificationSchema.statics.createCountryNotification = async function(countries: string[]) {
  const countryList = countries.length === 1 
    ? countries[0] 
    : countries.length === 2 
      ? countries.join(' and ')
      : `${countries.slice(0, -1).join(', ')}, and ${countries[countries.length - 1]}`;

  const notification = new this({
    type: 'country_added',
    title: '🌍 New Countries Added!',
    message: `Holidays for ${countryList} ${countries.length === 1 ? 'has' : 'have'} been added to Holivia. Check them out now!`,
    data: {
      countries,
      addedAt: new Date()
    },
    isGlobal: true,
    isActive: true
  });

  return await notification.save();
};

export default mongoose.model<INotification>('Notification', NotificationSchema);
