import mongoose, { Document, Schema } from 'mongoose';

export interface IHoliday extends Document {
  name: string;
  date: Date;
  year: number;
  country: string;
  type: 'national' | 'religious' | 'cultural' | 'regional';
  description: string;
  history?: string;
  activities?: string[];
  isRecurring: boolean;
  recurringPattern?: 'yearly' | 'lunar' | 'custom';
  imageUrl?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const HolidaySchema: Schema = new Schema({
  name: {
    type: String,
    required: [true, 'Holiday name is required'],
    trim: true,
    maxlength: [100, 'Holiday name cannot exceed 100 characters']
  },
  date: {
    type: Date,
    required: [true, 'Holiday date is required']
  },
  year: {
    type: Number,
    required: [true, 'Year is required'],
    min: [2020, 'Year must be 2020 or later'],
    max: [2030, 'Year cannot exceed 2030']
  },
  country: {
    type: String,
    required: [true, 'Country is required'],
    default: 'Nigeria',
    trim: true
  },
  type: {
    type: String,
    required: [true, 'Holiday type is required'],
    enum: ['national', 'religious', 'cultural', 'regional'],
    default: 'national'
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  history: {
    type: String,
    trim: true,
    maxlength: [1000, 'History cannot exceed 1000 characters']
  },
  activities: [{
    type: String,
    trim: true,
    maxlength: [200, 'Activity description cannot exceed 200 characters']
  }],
  isRecurring: {
    type: Boolean,
    default: true
  },
  recurringPattern: {
    type: String,
    enum: ['yearly', 'lunar', 'custom'],
    default: 'yearly'
  },
  imageUrl: {
    type: String,
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
HolidaySchema.index({ date: 1 });
HolidaySchema.index({ country: 1, year: 1 });
HolidaySchema.index({ type: 1 });
HolidaySchema.index({ isActive: 1 });

// Virtual for days until holiday
HolidaySchema.virtual('daysUntil').get(function(this: IHoliday) {
  const today = new Date();
  const holidayDate = new Date(this.date);
  const diffTime = holidayDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
});

// Virtual for formatted date
HolidaySchema.virtual('formattedDate').get(function(this: IHoliday) {
  return this.date.toLocaleDateString('en-NG', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
});

export default mongoose.model<IHoliday>('Holiday', HolidaySchema);
