import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import { connectDB } from './config/database';
import holidayRoutes from './routes/holidayRoutes';
import adminRoutes from './routes/adminRoutes';
import userRoutes from './routes/userRoutes';
import notificationRoutes from './routes/notificationRoutes';

// Load environment variables
dotenv.config();

const app = express();
app.set('trust proxy', 1); // Trust proxy for Vercel/production
const PORT = process.env.PORT || 5000;

// Connect to MongoDB (for local development and serverless functions)
if (process.env.VERCEL !== '1') {
  // Only connect directly in local development
  connectDB().catch(console.error);
}

// Security middleware
app.use(helmet());
// Allow all origins for CORS (for development and mobile app compatibility)
app.use(cors({
  origin: '*',
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Logging middleware
app.use(morgan('combined'));

// Health check endpoint
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'Holivia API is running',
    timestamp: new Date().toISOString()
  });
});

// API routes
app.use('/api/holidays', holidayRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/users', userRoutes);
app.use('/api/notifications', notificationRoutes);

// 404 handler
app.use('*', (_req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Global error handler
app.use((err: any, _req: express.Request, res: express.Response, _next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'Something went wrong!',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Only start the server if not running in a serverless environment
if (process.env.VERCEL !== '1') {
  app.listen(Number(PORT), '0.0.0.0', () => {
    console.log(`🚀 Holivia API server running on port ${PORT}`);
    console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`📱 Server accessible at:`);
    console.log(`   - Local: http://localhost:${PORT}`);
    console.log(`   - Network: http://***********:${PORT}`);
    console.log(`   - Alt Network: http://*************:${PORT}`);
  });
}

export default app;
