import mongoose from 'mongoose';

// Global variable to cache the connection
let cachedConnection: typeof mongoose | null = null;

export const connectDB = async (): Promise<typeof mongoose> => {
  // If we have a cached connection, return it
  if (cachedConnection && mongoose.connection.readyState === 1) {
    return cachedConnection;
  }

  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/holivia';

    console.log('🔄 Attempting to connect to MongoDB...');
    console.log('📍 MongoDB URI:', mongoURI.replace(/\/\/.*@/, '//***:***@')); // Hide credentials

    // Configure mongoose for serverless
    mongoose.set('bufferCommands', false);

    const connectionOptions = {
      serverSelectionTimeoutMS: 10000, // Increase timeout for local development
      socketTimeoutMS: 45000, // Close sockets after 45s of inactivity
      maxPoolSize: 10, // Maintain up to 10 socket connections
      minPoolSize: 5, // Maintain a minimum of 5 socket connections
      maxIdleTimeMS: 30000, // Close connections after 30s of inactivity
    };

    const connection = await mongoose.connect(mongoURI, connectionOptions);

    console.log('✅ MongoDB connected successfully');
    console.log('📊 Connection state:', mongoose.connection.readyState);

    // Cache the connection
    cachedConnection = connection;

    return connection;

  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    console.error('🔍 Connection details:');
    console.error('  - Ready state:', mongoose.connection.readyState);
    if (error instanceof Error) {
      console.error('  - Error name:', error.name);
      console.error('  - Error message:', error.message);
    }

    if (process.env.VERCEL === '1') {
      throw error; // Don't exit process in serverless environment
    } else {
      // In local development, we can be more forgiving
      console.error('⚠️ Continuing without database connection for local development');
      throw error;
    }
  }
};
