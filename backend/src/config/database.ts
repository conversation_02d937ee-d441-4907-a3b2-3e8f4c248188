import mongoose from 'mongoose';

// Global variable to cache the connection
let cachedConnection: typeof mongoose | null = null;

export const connectDB = async (): Promise<typeof mongoose> => {
  // If we have a cached connection, return it
  if (cachedConnection && mongoose.connection.readyState === 1) {
    return cachedConnection;
  }

  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/holivia';

    // Configure mongoose for serverless
    mongoose.set('bufferCommands', false);

    const connection = await mongoose.connect(mongoURI, {
      serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s
      socketTimeoutMS: 45000, // Close sockets after 45s of inactivity
      maxPoolSize: 10, // Maintain up to 10 socket connections
      minPoolSize: 5, // Maintain a minimum of 5 socket connections
      maxIdleTimeMS: 30000, // Close connections after 30s of inactivity
    });

    console.log('✅ MongoDB connected successfully');

    // Cache the connection
    cachedConnection = connection;

    return connection;

  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    throw error; // Don't exit process in serverless environment
  }
};
