{"name": "holivia-backend", "version": "1.0.0", "description": "Backend API for Holivia - Local Public Holiday Reminder App", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "seed": "ts-node src/scripts/seed.ts", "vercel-build": "echo 'No build step required for Vercel'", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["holidays", "api", "express", "mongodb", "typescript"], "author": "<PERSON>", "license": "MIT", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.0", "morgan": "^1.10.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.0.3", "@vercel/node": "^3.0.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}