# Holivia - Local Public Holiday Reminder App

A beautiful React Native mobile application that helps users stay informed about public holidays in Nigeria. Built with modern technologies and featuring a stunning green gradient design.

## 🌟 Features

### Core Features
- 🗓️ **Holiday Calendar View** - Interactive calendar with highlighted holidays
- 📅 **Upcoming Holidays List** - View upcoming holidays with countdown
- 📖 **Holiday Details** - Comprehensive information about each holiday
- 🔔 **Smart Notifications** - Get reminded before holidays
- 🎉 **Activity Suggestions** - Discover what to do on holidays
- 🌍 **Multi-Country Support** - Currently supports Nigeria (expandable)
- 🔍 **Search Functionality** - Find holidays by name, date, or description

### Technical Features
- ⚡ **Offline-First** - Core holiday data available offline
- 🔄 **Real-time Sync** - Updates and notifications online
- 📱 **Cross-Platform** - Works on both iOS and Android
- 🎨 **Beautiful UI** - Modern design with green gradients
- 🔒 **Secure API** - Rate-limited and validated backend

## 🛠️ Tech Stack

### Frontend (React Native)
- **Framework**: React Native with Expo
- **Language**: TypeScript
- **Styling**: TailwindCSS with NativeWind
- **Navigation**: React Navigation v6
- **State Management**: React Hooks
- **HTTP Client**: Axios
- **UI Components**: Custom components with Expo Vector Icons
- **Gradients**: Expo Linear Gradient

### Backend (Node.js)
- **Framework**: Express.js
- **Language**: TypeScript
- **Database**: MongoDB with Mongoose
- **Validation**: express-validator
- **Security**: Helmet, CORS, Rate limiting
- **Logging**: Morgan
- **Environment**: dotenv

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or cloud)
- Expo CLI (`npm install -g @expo/cli`)
- iOS Simulator or Android Emulator (optional)

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd holivia-app
```

2. **Setup Backend**
```bash
cd backend
npm install
cp .env.example .env
# Edit .env with your MongoDB connection string
npm run seed  # Seed database with Nigerian holidays
npm run dev   # Start development server
```

3. **Setup Frontend**
```bash
cd ../frontend
npm install
npm start     # Start Expo development server
```

4. **Run the App**
- Scan QR code with Expo Go app (iOS/Android)
- Press `i` for iOS simulator
- Press `a` for Android emulator
- Press `w` for web browser

## 📱 App Structure

### Frontend Structure
```
frontend/
├── src/
│   ├── components/     # Reusable UI components
│   ├── screens/        # App screens
│   ├── navigation/     # Navigation configuration
│   ├── services/       # API services
│   ├── hooks/          # Custom React hooks
│   ├── utils/          # Utility functions
│   ├── types/          # TypeScript type definitions
│   └── constants/      # App constants and colors
├── assets/             # Images and static assets
└── App.tsx            # Main app component
```

### Backend Structure
```
backend/
├── src/
│   ├── controllers/    # Route controllers
│   ├── models/         # Database models
│   ├── routes/         # API routes
│   ├── middleware/     # Custom middleware
│   ├── config/         # Configuration files
│   ├── utils/          # Utility functions
│   └── scripts/        # Database scripts
└── index.ts           # Server entry point
```

## 🎨 Design System

### Color Palette
- **Primary Green**: #22c55e (Emerald 500)
- **Secondary Green**: #84cc16 (Lime 500)
- **Accent Blue**: #0ea5e9 (Sky 500)
- **Gradients**: Beautiful green gradients throughout

### Typography
- **Headings**: Bold, clear hierarchy
- **Body**: Readable, accessible fonts
- **Labels**: Consistent sizing and spacing

## 📊 API Endpoints

### Public Endpoints
- `GET /api/holidays` - Get all holidays with filtering
- `GET /api/holidays/upcoming` - Get upcoming holidays
- `GET /api/holidays/search` - Search holidays
- `GET /api/holidays/by-month/:year/:month/:country` - Get holidays by month
- `GET /api/holidays/:id` - Get holiday by ID

### Admin Endpoints
- `POST /api/admin/holidays` - Create holiday
- `PUT /api/admin/holidays/:id` - Update holiday
- `DELETE /api/admin/holidays/:id` - Delete holiday
- `GET /api/admin/stats` - Get statistics

## 🗃️ Database Schema

### Holiday Model
```typescript
{
  name: string;           // Holiday name
  date: Date;            // Holiday date
  year: number;          // Year
  country: string;       // Country (default: Nigeria)
  type: string;          // national | religious | cultural | regional
  description: string;   // Holiday description
  history?: string;      // Historical background
  activities?: string[]; // Suggested activities
  isRecurring: boolean;  // Recurring annually
  recurringPattern?: string; // yearly | lunar | custom
  imageUrl?: string;     // Optional image
  isActive: boolean;     // Active status
}
```

## 🔧 Development

### Available Scripts

**Backend:**
- `npm run dev` - Start development server
- `npm run build` - Build TypeScript
- `npm start` - Start production server
- `npm run seed` - Seed database

**Frontend:**
- `npm start` - Start Expo development server
- `npm run android` - Run on Android
- `npm run ios` - Run on iOS
- `npm run web` - Run on web

### Environment Variables

**Backend (.env):**
```env
PORT=5000
NODE_ENV=development
MONGODB_URI=mongodb://localhost:27017/holivia
FRONTEND_URL=http://localhost:3000
JWT_SECRET=your-secret-key
```

## 🚀 Deployment

### Backend Deployment
1. Build the application: `npm run build`
2. Deploy to your preferred platform (Heroku, Railway, etc.)
3. Set environment variables
4. Run database migrations/seeding

### Frontend Deployment
1. Build for production: `expo build`
2. Submit to app stores or deploy as web app
3. Update API endpoints for production

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Nigerian public holiday data
- Expo and React Native communities
- TailwindCSS for beautiful styling
- MongoDB for reliable data storage

## 📞 Support

For support, email <EMAIL> or create an issue in this repository.

---

**Made with ❤️ for Nigeria 🇳🇬**
